/**
 * Keycloak Service
 * 
 * Utilities for handling and extracting Keycloak IDs.
 */

/**
 * Validates if the ID looks like a valid Keycloak UUID
 */
export function isValidKeycloakId(id: string): boolean {
  if (!id) return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Attempt to extract or sanitize a Keycloak ID from various formats
 */
export function sanitizeKeycloakId(id: string): string | null {
  if (!id) return null;
  
  // Already valid
  if (isValidKeycloakId(id)) return id;
  
  // Try to extract UUID pattern
  const uuidPattern = /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i;
  const match = id.match(uuidPattern);
  if (match) return match[1];
  
  // Try UUID without dashes (32 chars)
  if (id.length === 32) {
    try {
      const withDashes = `${id.substring(0, 8)}-${id.substring(8, 12)}-${id.substring(12, 16)}-${id.substring(16, 20)}-${id.substring(20)}`;
      if (isValidKeycloakId(withDashes)) return withDashes;
    } catch {}
  }
  
  // Remove customer- prefix if present
  if (id.startsWith('customer-')) {
    const withoutPrefix = id.substring(9);
    if (isValidKeycloakId(withoutPrefix)) return withoutPrefix;
  }
  
  return null;
}

/**
 * Extract Keycloak ID from NextAuth session
 */
export function getKeycloakIdFromSession(session: any): string | null {
  if (!session) return null;
  
  // Try potential locations where Keycloak ID might be stored
  if (session.user?.sub) return session.user.sub;  // Standard OIDC claim
  if (session.user?.id) return session.user.id;    // NextAuth sometimes maps sub to id
  if (session.sub) return session.sub;             // Direct access to JWT payload
  
  // For Keycloak specifically
  if (session.user?.preferred_username) {
    try {
      // Sometimes Keycloak ID is embedded in the username
      const sanitized = sanitizeKeycloakId(session.user.preferred_username);
      if (sanitized) return sanitized;
    } catch {}
  }
  
  return null;
}

/**
 * Get Keycloak ID from token JWT
 */
export function getKeycloakIdFromToken(token: string): string | null {
  if (!token) return null;
  
  try {
    const parts = token.split('.');
    if (parts.length === 3) {
      const payload = JSON.parse(atob(parts[1]));
      if (payload.sub) return payload.sub;
    }
  } catch {}
  
  return null;
}

/**
 * Get Keycloak ID from local storage
 */
export function getKeycloakIdFromStorage(): string | null {
  try {
    // Check various possible storage keys
    const keys = ['keycloak_id', 'user_id', 'id'];
    
    for (const key of keys) {
      const value = localStorage.getItem(key);
      if (value) {
        const sanitized = sanitizeKeycloakId(value);
        if (sanitized) return sanitized;
      }
    }
    
    // Look for token in storage and extract sub
    const token = localStorage.getItem('keycloak_token') || 
                  localStorage.getItem('token') ||
                  sessionStorage.getItem('keycloak_token') ||
                  sessionStorage.getItem('token');
    
    if (token) return getKeycloakIdFromToken(token);
  } catch {}
  
  return null;
}

/**
 * Get Keycloak ID from URL parameters
 */
export function getKeycloakIdFromParams(params: URLSearchParams): string | null {
  if (!params) return null;
  
  // Check various possible parameter names
  const keys = ['keycloak_id', 'customer_id', 'customerId', 'sub', 'id'];
  
  for (const key of keys) {
    const value = params.get(key);
    if (value) {
      const sanitized = sanitizeKeycloakId(value);
      if (sanitized) return sanitized;
      if (isValidKeycloakId(value)) return value;
    }
  }
  
  return null;
}

/**
 * Resolve Keycloak ID from multiple sources, with priority order
 */
export function resolveKeycloakId({
  session = null,
  token = null,
  params = null,
  useStorage = true
}: {
  session?: any;
  token?: string;
  params?: URLSearchParams;
  useStorage?: boolean;
}): {
  keycloakId: string | null;
  source: string;
  isValid: boolean;
} {
  let keycloakId: string | null = null;
  let source = 'none';
  
  // Priority 1: URL Parameters (highest priority as it's from current context)
  if (params) {
    keycloakId = getKeycloakIdFromParams(params);
    if (keycloakId) source = 'params';
  }
  
  // Priority 2: Token
  if (!keycloakId && token) {
    keycloakId = getKeycloakIdFromToken(token);
    if (keycloakId) source = 'token';
  }
  
  // Priority 3: Session
  if (!keycloakId && session) {
    keycloakId = getKeycloakIdFromSession(session);
    if (keycloakId) source = 'session';
  }
  
  // Priority 4: Local Storage (lowest priority as it could be outdated)
  if (!keycloakId && useStorage) {
    keycloakId = getKeycloakIdFromStorage();
    if (keycloakId) source = 'storage';
  }
  
  // Check if the ID is in valid UUID format
  const isValid = keycloakId ? isValidKeycloakId(keycloakId) : false;
  
  return { keycloakId, source, isValid };
} 