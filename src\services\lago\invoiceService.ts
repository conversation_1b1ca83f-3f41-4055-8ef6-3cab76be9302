/**
 * Lago Invoice Service
 *
 * Handles interactions with Lago Invoice API endpoints.
 * Includes functionality for updating invoice payment status and sending email notifications.
 */

import { sendInvoicePaymentEmail, sendInvoicePaymentEmailServer } from '@/src/services/emailService';

// Types
export interface Invoice {
  id: string;
  lago_id: string;
  sequential_id: number;
  number: string;
  issuing_date: string;
  payment_status: string;
  status: string;
  customer_id: string;
  amount_cents: number;
  amount_currency: string;
  total_amount_cents: number;
  subscription_id?: string;
  created_at: string;
}

export interface InvoiceResponse {
  invoices: Invoice[];
  meta: {
    current_page: number;
    next_page: number | null;
    prev_page: number | null;
    total_pages: number;
    total_count: number;
  };
}

/**
 * Get invoices for a customer by external_customer_id, filtered by date, payment_status and status
 * This is used to find the latest invoice generated after subscription creation
 */
export async function getLatestInvoiceByCustomerId(
  customerId: string,
  dateFrom: string
): Promise<Invoice | null> {
  if (!customerId || !dateFrom) {
    console.error('❌ LAGO INVOICE - Cannot get latest invoice: Missing parameters', { customerId, dateFrom });
    return null;
  }

  console.log(`🔄 LAGO INVOICE - Getting latest invoice for customer: ${customerId} from date: ${dateFrom}`);

  try {
    const queryParams = new URLSearchParams({
      external_customer_id: customerId,
      issuing_date_from: dateFrom,
      payment_status: 'pending',
      status: 'finalized'
    });

    const response = await fetch(`/api/lago/invoice?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log(`🔄 LAGO INVOICE - Get latest invoice response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      const invoices = data.invoices || [];

      if (invoices.length === 0) {
        console.log(`ℹ️ LAGO INVOICE - No invoices found for customer: ${customerId} from date: ${dateFrom}`);
        return null;
      }

      // Get the most recent invoice (assuming they are sorted by date)
      const latestInvoice = invoices[0];
      console.log(`✅ LAGO INVOICE - Retrieved latest invoice: ${latestInvoice.lago_id} for customer: ${customerId}`);
      return latestInvoice;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO INVOICE - API error: ${response.status}`, errorText);
      return null;
    }
  } catch (error) {
    console.error(`❌ LAGO INVOICE - Exception getting latest invoice:`, error);
    return null;
  }
}

/**
 * Get a specific invoice by ID
 */
export async function getInvoiceById(invoiceId: string): Promise<Invoice | null> {
  if (!invoiceId) {
    console.error('❌ LAGO INVOICE - Cannot get invoice: Missing invoice ID');
    return null;
  }

  console.log(`🔄 LAGO INVOICE - Getting invoice: ${invoiceId}`);

  try {
    const response = await fetch(`/api/lago/invoice?id=${encodeURIComponent(invoiceId)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log(`🔄 LAGO INVOICE - Get invoice response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ LAGO INVOICE - Retrieved invoice: ${invoiceId}`);
      return data.invoice || null;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO INVOICE - API error: ${response.status}`, errorText);
      return null;
    }
  } catch (error) {
    console.error(`❌ LAGO INVOICE - Exception getting invoice:`, error);
    return null;
  }
}

/**
 * Update invoice payment status to 'succeeded'
 *
 * @param invoiceId The Lago invoice ID
 * @param sendEmail Whether to send an email notification (default: true)
 * @param trackingId Optional tracking ID for logging
 * @returns Promise resolving to a boolean indicating success
 */
export async function updateInvoicePaymentStatus(
  invoiceId: string,
  sendEmail: boolean = true,
  trackingId?: string
): Promise<boolean> {
  if (!invoiceId) {
    console.error('❌ LAGO INVOICE - Cannot update payment status: Missing invoice ID');
    return false;
  }

  // Generate a tracking ID if not provided - this will be used for both invoice update and email
  const generatedTrackingId = trackingId || `inv_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
  const logPrefix = `🔄 LAGO INVOICE [${generatedTrackingId}]`;

  console.log(`${logPrefix} - Updating payment status to "succeeded" for invoice: ${invoiceId}`);
  console.log(`${logPrefix} - Using tracking ID: ${generatedTrackingId} for both invoice update and email`);

  try {
    // Call the API to update the invoice payment status
    const response = await fetch('/api/lago/invoice', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Tracking-ID': generatedTrackingId
      },
      body: JSON.stringify({
        invoice_id: invoiceId
      })
    });

    console.log(`${logPrefix} - Update payment status response: ${response.status}`);

    if (response.ok) {
      console.log(`✅ ${logPrefix} - Successfully updated invoice payment status to "succeeded": ${invoiceId}`);

      // Parse the response to get any additional data
      let responseData;
      try {
        responseData = await response.json();
        console.log(`${logPrefix} - Response data:`, {
          invoice_id: responseData.invoice?.id,
          lago_id: responseData.invoice?.lago_id,
          payment_status: responseData.invoice?.payment_status
        });
      } catch (parseError) {
        console.warn(`${logPrefix} - Could not parse response JSON:`, parseError);
      }

      // Send email notification if requested
      if (sendEmail) {
        try {
          console.log(`📧 ${logPrefix} - Sending invoice payment email for invoice: ${invoiceId}`);
          console.log(`📧 ${logPrefix} - Using same tracking ID for email: ${generatedTrackingId}`);

          // Use the server-side function directly to avoid additional API calls
          // This ensures the email is sent server-side and only once
          const emailResult = await sendInvoicePaymentEmailServer(invoiceId, generatedTrackingId);

          if (emailResult.success) {
            if (emailResult.message === 'Email already sent (deduplicated)') {
              console.log(`📧 ${logPrefix} - Email already sent for invoice: ${invoiceId}, skipping duplicate`);
            } else {
              console.log(`✅ ${logPrefix} - Successfully sent invoice payment email: ${invoiceId}`);
            }
          } else {
            console.error(`⚠️ ${logPrefix} - Failed to send invoice payment email: ${invoiceId}`, emailResult.error);
            // Continue despite email failure - don't fail the whole operation
          }
        } catch (emailError) {
          console.error(`⚠️ ${logPrefix} - Exception sending invoice payment email: ${invoiceId}`, emailError);
          // Continue despite email failure - don't fail the whole operation
        }
      }

      return true;
    } else {
      const errorText = await response.text();
      console.error(`❌ ${logPrefix} - API error updating payment status: ${response.status}`, errorText);
      return false;
    }
  } catch (error) {
    console.error(`❌ LAGO INVOICE - Exception updating payment status:`, error);
    return false;
  }
}

/**
 * Get pending invoices for a customer
 *
 * This function fetches all pending invoices for a customer that are finalized
 * but not yet marked as paid. This is useful for checking if there are any
 * invoices that need to be updated after a payment is completed.
 *
 * @param customerId The Keycloak ID of the customer
 * @param dateFrom Optional date to filter invoices from (YYYY-MM-DD format)
 * @returns Array of pending invoices or null if error
 */
export async function getPendingInvoicesForCustomer(
  customerId: string,
  dateFrom?: string
): Promise<Invoice[] | null> {
  if (!customerId) {
    console.error('❌ LAGO INVOICE - Cannot get pending invoices: Missing customer ID');
    return null;
  }

  console.log(`🔄 LAGO INVOICE - Getting pending invoices for customer: ${customerId}`);

  try {
    const queryParams = new URLSearchParams({
      external_customer_id: customerId,
      payment_status: 'pending',
      status: 'finalized'
    });

    // Add date filter if provided
    if (dateFrom) {
      queryParams.append('issuing_date_from', dateFrom);
    }

    const response = await fetch(`/api/lago/invoice?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log(`🔄 LAGO INVOICE - Get pending invoices response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json() as InvoiceResponse;
      const invoices = data.invoices || [];

      console.log(`✅ LAGO INVOICE - Retrieved ${invoices.length} pending invoices for customer: ${customerId}`);
      return invoices;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO INVOICE - API error getting pending invoices: ${response.status}`, errorText);
      return null;
    }
  } catch (error) {
    console.error(`❌ LAGO INVOICE - Exception getting pending invoices:`, error);
    return null;
  }
}

/**
 * Update payment status for multiple invoices
 *
 * This function updates the payment status for multiple invoices to 'succeeded'.
 * It processes each invoice sequentially and returns the results.
 *
 * @param invoiceIds Array of invoice IDs to update
 * @param sendEmail Whether to send email notifications (default: true)
 * @param trackingId Optional tracking ID for logging
 * @returns Object with success count and failed IDs
 */
export async function updateMultipleInvoicePaymentStatuses(
  invoiceIds: string[],
  sendEmail: boolean = true,
  trackingId?: string
): Promise<{ successCount: number; failedIds: string[] }> {
  if (!invoiceIds || invoiceIds.length === 0) {
    console.error('❌ LAGO INVOICE - Cannot update payment statuses: No invoice IDs provided');
    return { successCount: 0, failedIds: [] };
  }

  const logPrefix = trackingId ? `🔄 LAGO INVOICE [${trackingId}]` : '🔄 LAGO INVOICE';
  console.log(`${logPrefix} - Updating payment status for ${invoiceIds.length} invoices`);

  const results = {
    successCount: 0,
    failedIds: [] as string[]
  };

  // Process each invoice sequentially to avoid overwhelming the API
  for (const invoiceId of invoiceIds) {
    const success = await updateInvoicePaymentStatus(invoiceId, sendEmail, trackingId);

    if (success) {
      results.successCount++;
    } else {
      results.failedIds.push(invoiceId);
    }

    // Small delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  console.log(`✅ LAGO INVOICE - Updated ${results.successCount}/${invoiceIds.length} invoices successfully`);

  if (results.failedIds.length > 0) {
    console.error(`❌ LAGO INVOICE - Failed to update ${results.failedIds.length} invoices:`, results.failedIds);
  }

  return results;
}