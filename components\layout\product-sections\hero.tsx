import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BackgroundBeamsWithCollision } from "@/components/ui/extras/background-beams-with-collision";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import { TrialButton } from "@/components/trial/trial-button";

type HeroSectionProps = {
  title?: string;
  subtitle?: string;
  description?: string;
  buttons?: Array<{ text: string }> | { text: string };
  heroImage?: { url: string };
  productSlug?: string;
  productName?: string;
  trialPlan?: {
    name: string;
    tag?: string;
    plan_code: string;
    trialDurationInDays: number;
    features?: { feature: string; isIncluded: boolean; id: string }[];
    description?: string;
    button?: { text: string; id?: string };
  };
  onScrollToPricing?: () => void;
};

export const HeroSection = (
  { 
    title, 
    description, 
    buttons = [], 
    heroImage, 
    productSlug, 
    productName, 
    trialPlan,
    onScrollToPricing 
  }: HeroSectionProps
) => {
  // Normalize buttons to always be an array
  const normalizedButtons = Array.isArray(buttons) ? buttons : [buttons];

  // Find the trial button by text (looking for words like "free", "trial", etc.)
  const trialButtonIndex = normalizedButtons.findIndex(button => 
    button.text?.toLowerCase().includes("trial") || 
    button.text?.toLowerCase().includes("free") ||
    button.text?.toLowerCase().includes("start") ||
    button.text?.toLowerCase().includes("get started") ||
    button.text?.toLowerCase().includes("try")
  );

  // Create new array of buttons, potentially replacing the trial button
  const renderButtons = () => {
    // If we found a trial button and have trial plan data, replace it with our TrialButton
    if (trialPlan && trialButtonIndex >= 0) {
      return normalizedButtons.map((button, index) => {
        if (index === trialButtonIndex) {
          // This is the trial button, use our custom component
          return (
            <TrialButton
              key={index}
              buttonText={button.text || "Start Free Trial"}
              productSlug={productSlug || ""}
              productName={productName || title || ""}
              trialPlan={trialPlan}
              className="w-5/6 md:w-1/4 font-bold group/arrow"
              onScrollToPricing={onScrollToPricing}
            />
          );
        }
        
        // Regular button
        return (
          <Button key={index} className="w-5/6 md:w-1/4 font-bold group/arrow">
            {button.text}
            <ArrowRight className="size-5 ml-2 group-hover/arrow:translate-x-1 transition-transform" />
          </Button>
        );
      });
    }
    
    // If no trial button found but we have a trial plan, add it as a new button
    if (trialPlan && trialButtonIndex < 0) {
      return (
        <>
          {normalizedButtons.map((button, index) => (
            <Button key={index} className="w-5/6 md:w-1/4 font-bold group/arrow">
              {button.text}
              <ArrowRight className="size-5 ml-2 group-hover/arrow:translate-x-1 transition-transform" />
            </Button>
          ))}
          
          <TrialButton
            buttonText={trialPlan.button?.text || "Start Free Trial"}
            productSlug={productSlug || ""}
            productName={productName || title || ""}
            trialPlan={trialPlan}
            className="w-5/6 md:w-1/4 font-bold group/arrow"
            onScrollToPricing={onScrollToPricing}
          />
        </>
      );
    }
    
    // No trial plan or no trial button found, just render regular buttons
    return normalizedButtons.map((button, index) => (
      <Button key={index} className="w-5/6 md:w-1/4 font-bold group/arrow">
        {button.text}
        <ArrowRight className="size-5 ml-2 group-hover/arrow:translate-x-1 transition-transform" />
      </Button>
    ));
  };

  return (
    <section className="container w-full">
      <div className="grid place-items-center lg:max-w-screen-xl mx-auto py-16 md:py-32">
        <BackgroundBeamsWithCollision>
          <div className="text-center space-y-8 pb-20">
            {/* <Badge variant="outline" className="text-sm bg-muted py-2">
              <span className="mr-2 text-primary">
                <Badge className="bg-background text-foreground hover:bg-background">
                  New
                </Badge>
              </span>
              <span> Design is out now! </span>
            </Badge> */}
            <div className="max-w-screen-md mx-auto text-center text-4xl md:text-6xl font-bold">
              <h1>{title}</h1>
            </div>
            <p className="max-w-screen-sm mx-auto text-xl text-muted-foreground">
              {description}
            </p>
            <div className="space-y-4 md:space-y-0 md:space-x-4">
              {renderButtons()}

              <Button variant="secondary" className="w-5/6 md:w-1/4 font-bold">
                Learn More
              </Button>
            </div>
          </div>
        </BackgroundBeamsWithCollision>

        <div className="relative group">
          {/* blur effect */}
          <div className="absolute top-2 lg:-top-8 left-1/2 transform -translate-x-1/2 w-[90%] mx-auto h-24 lg:h-80 bg-primary/60 rounded-full blur-3xl"></div>
          {/* blur effect */}

          <Image
            width={1240}
            height={1200}
            className="w-full mx-auto rounded-lg relative rouded-lg leading-none flex items-center dark:hidden"
            src={heroImage?.url || ""}
            alt="hero image light"
          />
          <Image
            width={1240}
            height={1200}
            className="w-full mx-auto rounded-lg relative rouded-lg leading-none flex items-center hidden dark:block"
            src={heroImage?.url || ""}
            alt="hero image dark"
          />

          <div className="absolute bottom-0 left-0 w-full h-20 md:h-32 bg-gradient-to-b from-background/0 via-background/60 to-background rounded-lg"></div>
        </div>
      </div>
    </section>
  );
};
