import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// List of known product slugs that should be redirected to /products/[slug]
const PRODUCT_SLUGS = [
  'onesociety',
  'onegate',
  'onepos',
  'onefooddialer'
];

export function middleware(request: NextRequest) {
  // Check for successful payment flag
  const paymentSuccessTimestamp = request.cookies.get("payment_success_timestamp");
  const cartCleared = request.cookies.get("cart_cleared");

  // If we have a record of successful payment and cart wasn't cleared yet
  // we'll set a cookie to ensure the client-side knows to clear the cart
  if (paymentSuccessTimestamp && !cartCleared) {
    const response = NextResponse.next();

    // Set cart_cleared cookie with proper attributes
    response.cookies.set("cart_cleared", "true", {
      path: "/",
      maxAge: 60 * 60 * 24, // 1 day
      sameSite: "lax", // Changed from strict to lax for better cross-origin handling
      secure: process.env.NODE_ENV === "production" // Only secure in production
    });

    // Keep the original timestamp to ensure proper tracking
    response.cookies.set("payment_success_timestamp", paymentSuccessTimestamp.value, {
      path: "/",
      maxAge: 60 * 60 * 24, // 1 day
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production"
    });

    return response;
  }

  // Handle product slug redirects
  // Check if the URL path is one of our known product slugs at the root level
  const pathname = request.nextUrl.pathname;

  // Check if the path matches a known product slug directly at the root
  // For example: /onesociety -> /products/onesociety
  const matchedSlug = PRODUCT_SLUGS.find(slug => pathname === `/${slug}`);

  if (matchedSlug) {
    // Get the hash fragment if any
    const hash = request.nextUrl.hash;

    // Create the redirect URL with the hash preserved
    const redirectUrl = new URL(`/products/${matchedSlug}${hash}`, request.url);

    console.log(`Redirecting from ${pathname} to ${redirectUrl.pathname}${hash}`);
    return NextResponse.redirect(redirectUrl);
  }

  // Add your authentication logic here
  // For now, we'll just redirect to login if not on the login page
  if (!request.nextUrl.pathname.startsWith('/login')) {
    // You would typically check for authentication here
    // For now, we'll comment this out so it doesn't interfere with development
    // return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Match all paths except static assets, API routes, and Next.js internals
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
    // Specifically match known product slugs at the root level
    ...PRODUCT_SLUGS.map(slug => `/${slug}`)
  ],
};