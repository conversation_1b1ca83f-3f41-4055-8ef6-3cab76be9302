/**
 * Lago Subscription Service
 *
 * Handles all interactions with Lago Subscription API endpoints.
 * Ensures Keycloak ID is consistently used as external_id.
 */

// API Constants - Removed as we're using server-side API routes

// Import the customer service to ensure customer exists before creating subscription
import { checkCustomerById, getOr<PERSON>reate<PERSON>ustomer, CustomerData } from './customerService';
import { generatePlanCode, extractProductCode, extractPlanType, extractPlanDuration } from '@/src/utils/plan-utils';

// Types
export interface CartItem {
  id: string;
  name: string;
  planCode?: string;
  price?: number;
  productName?: string;
  planDuration?: 'trial' | 'monthly' | 'yearly' | 'quarterly' | 'weekly';
  planType?: string;
  trialDays?: number;
  quantity?: number;
  image?: string;
}

export interface SubscriptionResponse {
  subscription?: {
    id: string;
    external_id: string;
    plan_code: string;
    status: string;
    customer_id: string;
    created_at: string;
  };
  error?: string;
}

export interface CustomerSubscriptionsResponse {
  subscriptions?: Array<{
    id: string;
    external_id: string;
    plan_code: string;
    status: string;
    customer_id: string;
    created_at: string;
    name?: string;
    next_billing_date?: string;
  }>;
  error?: string;
  meta?: {
    current_page: number;
    total_pages: number;
    total_count: number;
  };
}

/**
 * Validates and normalizes a plan code to ensure it follows the correct format
 * @param planCode Original plan code
 * @param options Options for normalization (productName, planType, planDuration, isTrial)
 * @returns A normalized plan code
 */
export function validateAndNormalizePlanCode(
  planCode: string,
  options: {
    productName?: string;
    planType?: string;
    planDuration?: string;
    isTrial?: boolean;
  } = {}
): string {
  if (!planCode) {
    throw new Error('Plan code is required');
  }

  const { productName, planType, planDuration, isTrial = false } = options;

  // Check if the plan code already follows the correct format
  const extractedProduct = extractProductCode(planCode);
  const extractedType = extractPlanType(planCode);
  const extractedDuration = extractPlanDuration(planCode);

  // If the plan code is already valid, return it
  if (extractedProduct && (isTrial || extractedDuration)) {
    console.log(`✅ PLAN CODE - Plan code ${planCode} is already in the correct format`);
    return planCode;
  }

  // If we have the product name, we can generate a valid plan code
  if (productName) {
    // Clean product name to use as code (lowercase, replace spaces with underscore)
    const normalizedProductName = productName.toLowerCase().replace(/[^\w]/g, '_');

    // Use extracted components or provided values
    const finalProductName = extractedProduct || normalizedProductName;
    const finalPlanType = extractedType || planType || 'standard';
    const finalPlanDuration = isTrial ? 'trial' : (extractedDuration || planDuration || 'monthly');

    // Generate proper plan code
    const generatedPlanCode = generatePlanCode(
      finalProductName,
      finalPlanType,
      finalPlanDuration,
      isTrial
    );

    console.log(`🔄 PLAN CODE - Generated normalized plan code: ${generatedPlanCode} from ${planCode}`);
    return generatedPlanCode;
  }

  // If we can't generate a new one, return the original
  console.log(`⚠️ PLAN CODE - Could not normalize plan code: ${planCode}, using as is`);
  return planCode;
}

/**
 * Create a subscription in Lago
 * @param keycloakId The Keycloak ID (customer external_id)
 * @param item The cart item to subscribe to
 * @param customerEmail Optional customer email for logging
 * @returns The subscription response or null if failed
 */
export async function createSubscription(
  keycloakId: string,
  item: CartItem,
  customerEmail?: string
): Promise<SubscriptionResponse | null> {
  if (!keycloakId) {
    console.error('❌ LAGO SUBSCRIPTION - Cannot create subscription: Missing Keycloak ID');
    return null;
  }

  if (!item || !item.planCode) {
    console.error('❌ LAGO SUBSCRIPTION - Cannot create subscription: Missing item or plan code');
    return null;
  }

  // Validate and normalize plan code
  try {
    const isTrial = item.planDuration === 'trial';
    const normalizedPlanCode = validateAndNormalizePlanCode(
      item.planCode,
      {
        productName: item.productName || item.name,
        planType: item.planType,
        planDuration: item.planDuration,
        isTrial
      }
    );

    // Update item plan code if it was normalized
    if (normalizedPlanCode !== item.planCode) {
      console.log(`🔄 LAGO SUBSCRIPTION - Normalized plan code from ${item.planCode} to ${normalizedPlanCode}`);
      item.planCode = normalizedPlanCode;
    }
  } catch (error) {
    console.error('❌ LAGO SUBSCRIPTION - Error normalizing plan code:', error);
    // Continue with the original plan code
  }

  console.log(`🔄 LAGO SUBSCRIPTION - Creating subscription for Keycloak ID: ${keycloakId}, Plan: ${item.planCode}`);

  // First ensure the customer exists
  const existingCustomer = await checkCustomerById(keycloakId);

  if (!existingCustomer) {
    console.log(`ℹ️ LAGO SUBSCRIPTION - Customer not found, creating customer first`);

    // Create minimal customer data
    const customerData: CustomerData = {
      external_id: keycloakId,
      email: customerEmail,
      name: 'Customer', // Default name if not provided
      country: 'IN',
      currency: 'INR',
      customer_type: 'individual'
    };

    const customerResult = await getOrCreateCustomer(customerData);

    if (!customerResult) {
      console.error('❌ LAGO SUBSCRIPTION - Failed to create customer, cannot create subscription');
      return { error: 'Failed to create customer' };
    }

    console.log(`✅ LAGO SUBSCRIPTION - Customer created/verified for ID: ${keycloakId}`);
  } else {
    console.log(`✅ LAGO SUBSCRIPTION - Found existing customer for ID: ${keycloakId}`);
  }

  // Generate unique external_id for subscription
  const productCode = extractProductCode(item.planCode);
  const subscriptionExternalId = `${keycloakId}@${item.planCode}`;

  // Log the subscription details
  console.log(`📝 LAGO SUBSCRIPTION - Details:`, {
    customer_id: keycloakId,
    plan_code: item.planCode,
    subscription_id: subscriptionExternalId,
    product: productCode || item.productName || item.name,
    duration: extractPlanDuration(item.planCode) || item.planDuration,
    type: extractPlanType(item.planCode) || item.planType
  });

  // Create subscription payload
  const now = new Date();
  // Set time 30 seconds behind actual time for API posting
  const adjustedNow = new Date(now.getTime() - 30 * 1000);
  const subscriptionDate = adjustedNow.toISOString();
  console.log(`🕒 LAGO SUBSCRIPTION - Using adjusted time: ${subscriptionDate} (30 seconds behind actual time: ${now.toISOString()})`);

  const payload = {
    subscription: {
      external_id: subscriptionExternalId,
      plan_code: item.planCode,
      external_customer_id: keycloakId,
      name: item.name || productCode,
      subscription_at: subscriptionDate
    }
  };

  try {
    // Use our server-side API route instead of direct Lago API call
    const response = await fetch('/api/lago/subscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    console.log(`🔄 LAGO SUBSCRIPTION - Create response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json() as SubscriptionResponse;
      console.log(`✅ LAGO SUBSCRIPTION - Created successfully for ID: ${keycloakId}, Plan: ${item.planCode}`);

      // Store the subscription ID in localStorage for reference
      try {
        if (data.subscription?.id) {
          // Store the latest subscription ID
          localStorage.setItem('latest_subscription_id', data.subscription.id);

          // Store the subscription creation timestamp
          if (data.subscription.created_at) {
            localStorage.setItem('latest_subscription_timestamp', data.subscription.created_at);
          }
        }
      } catch (storageError) {
        console.error(`❌ LAGO SUBSCRIPTION - Error storing subscription data:`, storageError);
      }

      return data;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO SUBSCRIPTION - API error: ${response.status}`, errorText);
      return { error: errorText };
    }
  } catch (error) {
    console.error(`❌ LAGO SUBSCRIPTION - Exception creating subscription:`, error);
    return { error: String(error) };
  }
}

/**
 * Get all subscriptions for a customer by Keycloak ID
 */
export async function getCustomerSubscriptions(keycloakId: string): Promise<CustomerSubscriptionsResponse | null> {
  if (!keycloakId) {
    console.error('❌ LAGO SUBSCRIPTION - Cannot get subscriptions: Missing Keycloak ID');
    return null;
  }

  console.log(`🔍 LAGO SUBSCRIPTION - Getting subscriptions for Keycloak ID: ${keycloakId}`);

  try {
    // Use our server-side API route
    const response = await fetch(`/api/lago/subscription?customerId=${encodeURIComponent(keycloakId)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log(`🔄 LAGO SUBSCRIPTION - Get response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ LAGO SUBSCRIPTION - Found ${data.subscriptions?.length || 0} subscriptions`);
      return data;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO SUBSCRIPTION - API error: ${response.status}`, errorText);
      return null;
    }
  } catch (error) {
    console.error(`❌ LAGO SUBSCRIPTION - Exception getting subscriptions:`, error);
    return null;
  }
}

/**
 * Cancel a subscription for a customer
 */
export async function cancelSubscription(subscriptionId: string): Promise<boolean> {
  if (!subscriptionId) {
    console.error('❌ LAGO SUBSCRIPTION - Cannot cancel: Missing subscription ID');
    return false;
  }

  console.log(`🔄 LAGO SUBSCRIPTION - Canceling subscription: ${subscriptionId}`);

  try {
    // Use our server-side API route
    const response = await fetch(`/api/lago/subscription?id=${encodeURIComponent(subscriptionId)}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log(`🔄 LAGO SUBSCRIPTION - Cancel response status: ${response.status}`);

    if (response.ok) {
      console.log(`✅ LAGO SUBSCRIPTION - Canceled successfully: ${subscriptionId}`);
      return true;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO SUBSCRIPTION - API error: ${response.status}`, errorText);
      return false;
    }
  } catch (error) {
    console.error(`❌ LAGO SUBSCRIPTION - Exception canceling subscription:`, error);
    return false;
  }
}