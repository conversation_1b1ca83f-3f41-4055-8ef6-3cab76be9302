# Use a minimal base image
FROM node:20-alpine AS builder

WORKDIR /app

# Install dependencies using a separate step to leverage caching
COPY package.json package-lock.json ./

RUN npm install

# Copy the rest of the application
COPY . .

# Build the Next.js app
#RUN npm run build

# Use a minimal runtime image
#FROM node:20-alpine AS runner

#WORKDIR /app

# Copy only necessary files from builder stage
#COPY --from=builder /app/.next ./.next
#COPY --from=builder /app/package.json ./
#COPY --from=builder /app/public ./public
#COPY --from=builder /app/node_modules ./node_modules

# Expose the port
EXPOSE 3000

CMD npm run dev
