"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
} from "@/components/ui/card";
import {
  Button,
  Badge,
  Separator,
  RadioGroup,
  RadioGroupItem,
  Label,
  Alert,
  AlertTitle,
  AlertDescription
} from "@/components/ui/";
import { Loader2, ArrowRight, Check, ChevronUp, ChevronDown, AlertCircle } from "lucide-react";
import { useUser } from "@/context/user-context";
import { useToast } from "@/components/ui/use-toast";
import { Subscription } from "@/src/services/trialService";
import { PricingData, SubscriptionPlan, getProductPlans, comparePlans, calculateYearlySavings } from "@/src/services/planService";
import { changePlan, calculateProration, ProrationResult } from "@/src/services/subscriptionService";
import { validatePlanChange } from "@/src/services/subscriptionValidationService";
import { PlanChangeBlockedDialog } from "./plan-change-blocked-dialog";

interface PlanChangeDialogProps {
  open: boolean;
  onClose: () => void;
  subscriptionId: string;
  currentPlanCode: string;
  productSlug: string;
  onSuccess?: () => void;
}

export function PlanChangeDialog({
  open,
  onClose,
  subscriptionId,
  currentPlanCode,
  productSlug,
  onSuccess
}: PlanChangeDialogProps) {
  const { userId } = useUser();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pricingData, setPricingData] = useState<PricingData | null>(null);
  const [selectedPlanCode, setSelectedPlanCode] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedBillingCycle, setSelectedBillingCycle] = useState<"monthly" | "yearly">("monthly");
  const [prorationResult, setProrationResult] = useState<ProrationResult | null>(null);
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);
  const [showBlockedDialog, setShowBlockedDialog] = useState(false);
  const [blockedMessage, setBlockedMessage] = useState<string>("");

  // Check if plan changes are allowed and load plans when dialog opens
  useEffect(() => {
    if (!open || !productSlug || !userId) return;

    const checkAndFetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Validate if plan change is allowed
        const validationResult = await validatePlanChange({
          userId,
          productSlug,
          planCode: currentPlanCode
        });

        if (!validationResult.isValid) {
          // Plan change is not allowed, show blocked dialog
          setBlockedMessage(validationResult.message);
          setShowBlockedDialog(true);
          onClose(); // Close the main dialog
          return;
        }

        // If validation passes, continue with fetching data
        // Fetch pricing data
        const data = await getProductPlans(productSlug);
        setPricingData(data);

        // Get subscription details
        if (subscriptionId) {
          const response = await fetch(`/api/lago/subscription?subscriptionId=${subscriptionId}`);
          if (response.ok) {
            const data = await response.json();
            if (data?.subscription) {
              setCurrentSubscription(data.subscription as Subscription);
            }
          }
        }

        // Set current plan as default selection
        setSelectedPlanCode(currentPlanCode);
      } catch (err) {
        console.error("Error fetching plan data:", err);
        setError("Failed to load plan information. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    checkAndFetchData();
  }, [open, productSlug, subscriptionId, currentPlanCode, userId]);

  // Calculate proration when plan selection changes
  useEffect(() => {
    if (!selectedPlanCode || !currentPlanCode || selectedPlanCode === currentPlanCode || !currentSubscription) {
      setProrationResult(null);
      return;
    }

    const calculateProratedAmount = async () => {
      try {
        // Find selected plan
        const selectedPlan = findPlanByCode(selectedPlanCode);
        if (!selectedPlan) return;

        // Get price based on selected billing cycle
        const newPlanPrice = selectedBillingCycle === "monthly"
          ? selectedPlan.monthlyPricing * 100
          : selectedPlan.yearlyPricing * 100;

        // Calculate proration
        const result = await calculateProration(
          currentSubscription,
          selectedPlanCode,
          newPlanPrice
        );

        setProrationResult(result);
      } catch (err) {
        console.error("Error calculating proration:", err);
        setProrationResult(null);
      }
    };

    calculateProratedAmount();
  }, [selectedPlanCode, currentPlanCode, selectedBillingCycle, currentSubscription]);

  // Find a plan by code from pricing data
  const findPlanByCode = (planCode: string): SubscriptionPlan | null => {
    if (!pricingData?.subscriptionPlan || !planCode) return null;

    return pricingData.subscriptionPlan.find(plan =>
      plan.plan_code_monthly === planCode || plan.plan_code_yearly === planCode
    ) || null;
  };

  // Format currency amount
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get change type (upgrade/downgrade)
  const getChangeType = async (): Promise<"upgrade" | "downgrade" | "same" | null> => {
    if (!selectedPlanCode || !currentPlanCode) return null;
    if (selectedPlanCode === currentPlanCode) return "same";

    const comparison = await comparePlans(productSlug, currentPlanCode, selectedPlanCode);

    if (comparison === 1) return "upgrade";
    if (comparison === -1) return "downgrade";
    return "same";
  };

  // Handle plan change submission
  const handleChangePlan = async () => {
    if (!userId || !selectedPlanCode || !subscriptionId) {
      toast({
        title: "Error",
        description: "Missing required information to change plan",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Determine if this is an upgrade or downgrade
      const changeType = await getChangeType();

      // For downgrades, show confirmation
      if (changeType === "downgrade" && !window.confirm("Are you sure you want to downgrade your plan? Some features may become unavailable.")) {
        setIsSubmitting(false);
        return;
      }

      // Change the plan
      const result = await changePlan(
        userId,
        subscriptionId,
        selectedPlanCode,
        {
          // Cancel immediately for upgrades, at period end for downgrades
          cancelImmediately: changeType === "upgrade",
          // Add effective date if needed
          // effectiveDate: new Date().toISOString()
        }
      );

      if (result && result.subscription) {
        toast({
          title: "Plan Changed Successfully",
          description: `Your subscription has been updated to the new plan.`,
          variant: "default"
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }

        // Close dialog
        onClose();
      } else {
        throw new Error("Failed to change plan");
      }
    } catch (err) {
      console.error("Error changing plan:", err);
      setError(typeof err === 'string' ? err : "Failed to change plan. Please try again later.");

      toast({
        title: "Error",
        description: "There was a problem changing your plan. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Determine if current selection is different from current plan
  const isPlanChanged = (): boolean => {
    return selectedPlanCode !== currentPlanCode;
  };

  // Render plan options from pricing data
  const renderPlanOptions = () => {
    if (!pricingData?.subscriptionPlan) return null;

    return (
      <RadioGroup
        value={selectedPlanCode || ""}
        onValueChange={setSelectedPlanCode}
        className="gap-4"
      >
        {pricingData.subscriptionPlan.map(plan => {
          // Get the plan code based on selected billing cycle
          const planCode = selectedBillingCycle === "monthly"
            ? plan.plan_code_monthly
            : plan.plan_code_yearly;

          // Get pricing based on selected billing cycle
          const price = selectedBillingCycle === "monthly"
            ? plan.monthlyPricing
            : plan.yearlyPricing;

          // Calculate savings if yearly
          const savings = selectedBillingCycle === "yearly"
            ? calculateYearlySavings(plan.monthlyPricing, plan.yearlyPricing)
            : null;

          // Check if this is the current plan
          const isCurrentPlan = planCode === currentPlanCode;

          return (
            <div key={plan.id} className="relative">
              <Card
                className={`cursor-pointer border-2 ${
                  planCode === selectedPlanCode
                    ? "border-primary"
                    : "border-muted"
                }`}
                onClick={() => setSelectedPlanCode(planCode)}
              >
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{plan.name}</CardTitle>
                      <CardDescription className="text-sm mt-1">{plan.description}</CardDescription>
                    </div>
                    {plan.tag && (
                      <Badge className="bg-primary/10 text-primary hover:bg-primary/20">
                        {plan.tag}
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-baseline gap-1">
                    <span className="text-3xl font-bold">
                      {formatAmount(price)}
                    </span>
                    <span className="text-muted-foreground text-sm">
                      /{selectedBillingCycle}
                    </span>
                  </div>

                  {savings && (
                    <Badge className="mt-2 bg-green-500/10 text-green-600 hover:bg-green-500/20">
                      Save {savings}%
                    </Badge>
                  )}

                  <div className="mt-4 space-y-2">
                    {plan.features.slice(0, 3).map((feature, index) => (
                      <div key={`${plan.id}-feature-${index}`} className="flex items-center">
                        {feature.isIncluded ? (
                          <Check className="h-4 w-4 mr-2 text-green-500" />
                        ) : (
                          <div className="h-4 w-4 mr-2" />
                        )}
                        <span className={feature.isIncluded ? "" : "text-muted-foreground"}>
                          {feature.feature}
                        </span>
                      </div>
                    ))}

                    {plan.features.length > 3 && (
                      <div className="text-sm text-muted-foreground">
                        +{plan.features.length - 3} more features
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <div className="w-full flex justify-between items-center">
                    <RadioGroupItem
                      value={planCode}
                      id={`plan-${plan.id}`}
                      className="mr-2"
                    />
                    <Label htmlFor={`plan-${plan.id}`} className="flex-1 cursor-pointer">
                      Select Plan
                    </Label>

                    {isCurrentPlan && (
                      <Badge className="bg-blue-500/10 text-blue-600">
                        Current Plan
                      </Badge>
                    )}
                  </div>
                </CardFooter>
              </Card>
            </div>
          );
        })}
      </RadioGroup>
    );
  };

  // Render proration information
  const renderProrationSummary = async () => {
    if (!prorationResult || !selectedPlanCode) return null;

    // Get plan change type
    const changeType = await getChangeType();

    return (
      <Card className="mt-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Plan Change Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span>Current Plan Credit</span>
            <span>{formatAmount(prorationResult.creditAmountCents / 100)}</span>
          </div>
          <div className="flex justify-between">
            <span>New Plan Cost</span>
            <span>{formatAmount((prorationResult.newPlanMonthlyPrice || 0) / 100)}</span>
          </div>
          <Separator className="my-2" />
          <div className="flex justify-between font-bold">
            <span>{changeType === "upgrade" ? "Amount Due Now" : "Credit Applied"}</span>
            <span className={`${
              prorationResult.netDifference > 0 ? "text-primary" : "text-green-600"
            }`}>
              {formatAmount(Math.abs(prorationResult.netDifference || 0) / 100)}
            </span>
          </div>

          <div className="text-sm text-muted-foreground mt-2">
            {changeType === "upgrade"
              ? "You will be charged the difference immediately."
              : "Credit will be applied to your account for future billing."
            }
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render change plan button with appropriate label
  const renderChangeButton = async () => {
    const changeType = await getChangeType();

    let buttonText = "Change Plan";
    let buttonIcon = <ArrowRight className="h-4 w-4 ml-2" />;

    if (changeType === "upgrade") {
      buttonText = "Upgrade Plan";
      buttonIcon = <ChevronUp className="h-4 w-4 ml-2" />;
    } else if (changeType === "downgrade") {
      buttonText = "Downgrade Plan";
      buttonIcon = <ChevronDown className="h-4 w-4 ml-2" />;
    }

    return (
      <Button
        disabled={isSubmitting || !isPlanChanged() || !selectedPlanCode}
        onClick={handleChangePlan}
      >
        {isSubmitting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Processing...
          </>
        ) : (
          <>
            {buttonText}
            {buttonIcon}
          </>
        )}
      </Button>
    );
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Change Subscription Plan</DialogTitle>
          <DialogDescription>
            Select a new plan for your subscription. Any price differences will be prorated.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="py-8 flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <>
            <div className="flex justify-center mb-6">
              <div className="inline-flex items-center bg-muted p-1 rounded-lg">
                <Button
                  variant={selectedBillingCycle === "monthly" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedBillingCycle("monthly")}
                >
                  Monthly
                </Button>
                <Button
                  variant={selectedBillingCycle === "yearly" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedBillingCycle("yearly")}
                >
                  Yearly
                </Button>
              </div>
            </div>

            {renderPlanOptions()}

            {prorationResult && renderProrationSummary()}
          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          {!isLoading && renderChangeButton()}
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Blocked Dialog */}
    <PlanChangeBlockedDialog
      open={showBlockedDialog}
      onClose={() => setShowBlockedDialog(false)}
      message={blockedMessage}
      actionType="change"
    />
  );
}