/**
 * Lago Customer Service
 *
 * Handles all interactions with Lago Customer API endpoints.
 * Ensures Keycloak ID is consistently used as external_id.
 */

// Import API Constants
import { LAGO_API_URL, LAGO_API_KEY } from '@/src/constants/api';

// Validate that LAGO_API_URL is defined
if (!LAGO_API_URL) {
  console.error("❌ LAGO CUSTOMER - LAGO_API_URL is not defined. Please check your environment variables.");
}

const CUSTOMER_URL = `${LAGO_API_URL}/customers`;

// Types
export interface CustomerData {
  external_id: string;  // Keycloak ID
  email?: string;
  name?: string;
  legal_name?: string;  // For business name
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zipcode?: string;
  country?: string;
  currency?: string;
  tax_identification_number?: string;
  phone?: string;
  customer_type?: string;
}

/**
 * Validates if the provided ID looks like a valid Keycloak UUID
 */
export function isValidKeycloakId(id: string): boolean {
  if (!id) return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Logs validation details for debugging
 */
// function logIdValidation(id: string, source: string, isValid: boolean) {
//   console.log(`🔍 VALIDATION [${source}] - ${isValid ? "✅ Passed" : "❌ Failed"}:`, {
//     id,
//     format: "UUID",
//     length: id.length,
//     hasDashes: id.includes('-'),
//   });
// }

/**
 * Attempt to clean/sanitize non-standard ID formats
 */
export function sanitizeKeycloakId(id: string, source: string): string | null {
  if (!id) return null;

  // Already valid
  if (isValidKeycloakId(id)) return id;

  console.log(`⚠️ LAGO CUSTOMER - Attempting to sanitize ID from ${source}:`, id);

  // Try to extract UUID pattern
  const uuidPattern = /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i;
  const match = id.match(uuidPattern);
  if (match) {
    return match[1];
  }

  // Try UUID without dashes (32 chars)
  if (id.length === 32) {
    try {
      const withDashes = `${id.substring(0, 8)}-${id.substring(8, 12)}-${id.substring(12, 16)}-${id.substring(16, 20)}-${id.substring(20)}`;
      if (isValidKeycloakId(withDashes)) {
        return withDashes;
      }
    } catch (e) {
      console.error(`❌ LAGO CUSTOMER - Failed to reformat ID:`, e);
    }
  }

  // Remove customer- prefix if present
  if (id.startsWith('customer-')) {
    const withoutPrefix = id.substring(9);
    if (isValidKeycloakId(withoutPrefix)) {
      return withoutPrefix;
    }
  }

  return null;
}

/**
 * Check if a customer exists in Lago by Keycloak ID
 * @returns The customer object if found, null if not found
 */
export async function checkCustomerById(keycloakId: string): Promise<CustomerData | null> {
  if (!keycloakId) {
    console.error("❌ LAGO CUSTOMER - Cannot check customer: Missing Keycloak ID");
    return null;
  }

  // Validate or sanitize ID if possible
  const sanitizedId = sanitizeKeycloakId(keycloakId, "checkCustomerById");
  const idToUse = sanitizedId || keycloakId;

  console.log(`🔍 LAGO CUSTOMER - Checking if customer exists with ID: ${idToUse}`);

  try {
    // Use our server-side API route that will handle CORS issues
    const response = await fetch(`/api/lago/customer?id=${encodeURIComponent(idToUse)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    console.log(`🔄 LAGO CUSTOMER - Check response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ LAGO CUSTOMER - Found customer with ID: ${idToUse}`);
      return data;
    } else if (response.status === 404) {
      console.log(`ℹ️ LAGO CUSTOMER - Customer not found with ID: ${idToUse}`);
      return null;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO CUSTOMER - API error: ${response.status}`, errorText);
      return null;
    }
  } catch (error) {
    console.error(`❌ LAGO CUSTOMER - Exception checking customer:`, error);
    return null;
  }
}

/**
 * Search for customers by email
 * @returns Object with exists flag and customerId if found
 */
export async function checkCustomerByEmail(email: string): Promise<{exists: boolean, customerId: string | null}> {
  if (!email) {
    console.error("❌ LAGO CUSTOMER - Cannot check by email: Missing email");
    return {exists: false, customerId: null};
  }

  if (!LAGO_API_URL) {
    console.error("❌ LAGO CUSTOMER - Cannot check by email: LAGO_API_URL is undefined");
    return {exists: false, customerId: null};
  }

  console.log(`🔍 LAGO CUSTOMER - Checking if customer exists with email: ${email}`);

  try {
    // Note: This is a custom endpoint that would need to be implemented in the API
    const response = await fetch(`${CUSTOMER_URL}/search?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      const exists = data && data.customer && data.customer.external_id;
      const customerId = exists ? data.customer.external_id : null;

      console.log(`${exists ? '✅' : 'ℹ️'} LAGO CUSTOMER - ${exists ? 'Found' : 'Did not find'} customer with email: ${email}`);

      return {exists, customerId};
    } else {
      console.error(`❌ LAGO CUSTOMER - API error searching by email: ${response.status}`);
      return {exists: false, customerId: null};
    }
  } catch (error) {
    console.error(`❌ LAGO CUSTOMER - Exception searching by email:`, error);
    return {exists: false, customerId: null};
  }
}

/**
 * Create a new customer in Lago using Keycloak ID as external_id
 * @returns The created customer object if successful, null otherwise
 */
export async function createCustomer(customerData: CustomerData): Promise<CustomerData | null> {
  if (!customerData) {
    console.error("❌ LAGO CUSTOMER - Cannot create customer: Missing data");
    return null;
  }

  // CRITICAL: Always use Keycloak ID as external_id
  const keycloakId = customerData.external_id;

  if (!keycloakId) {
    console.error("❌ LAGO CUSTOMER - Cannot create customer: Missing Keycloak ID");
    return null;
  }

  // Validate or sanitize ID if possible
  const sanitizedId = sanitizeKeycloakId(keycloakId, "createCustomer");
  if (sanitizedId) {
    customerData.external_id = sanitizedId;
    console.log(`✅ LAGO CUSTOMER - Using sanitized Keycloak ID: ${sanitizedId}`);
  } else {
    console.log(`⚠️ LAGO CUSTOMER - Using original ID (not UUID format): ${keycloakId}`);
  }

  // Process GST information if provided
  if (customerData.tax_identification_number) {
    // Normalize GST number (uppercase and trim)
    customerData.tax_identification_number = customerData.tax_identification_number.trim().toUpperCase();

    // Validate GST number format
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    if (!gstRegex.test(customerData.tax_identification_number)) {
      console.error(`❌ LAGO CUSTOMER - Invalid GST number format: ${customerData.tax_identification_number}`);
      // Don't fail the whole customer creation, just log and continue without GST
      customerData.tax_identification_number = undefined;
      customerData.legal_name = undefined;
    } else {
      // Ensure business name is provided if GST number is valid
      if (!customerData.legal_name) {
        console.error(`❌ LAGO CUSTOMER - Business name is required when GST number is provided`);
        // Use a default business name if not provided
        customerData.legal_name = customerData.name || "Business Customer";
      } else {
        // Trim business name
        customerData.legal_name = customerData.legal_name.trim();
      }

      console.log(`✅ LAGO CUSTOMER - Valid GST details: ${customerData.tax_identification_number}, ${customerData.legal_name}`);
    }
  } else if (customerData.legal_name) {
    // If business name is provided but no GST, trim it
    customerData.legal_name = customerData.legal_name.trim();
  }

  console.log(`🔄 LAGO CUSTOMER - Creating customer with ID: ${customerData.external_id}`);

  // Set defaults for required fields
  const payload = {
    customer: {
      ...customerData,
      country: customerData.country || "IN",
      currency: customerData.currency || "INR",
      customer_type: customerData.customer_type || "individual",
      name: customerData.name || "Customer"
    }
  };

  try {
    // Use our server-side API route that will handle CORS issues
    const response = await fetch('/api/lago/customer', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    console.log(`🔄 LAGO CUSTOMER - Create response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ LAGO CUSTOMER - Created customer with ID: ${customerData.external_id}`);
      return data;
    } else {
      const errorText = await response.text();
      console.error(`❌ LAGO CUSTOMER - API error creating customer: ${response.status}`, errorText);
      return null;
    }
  } catch (error) {
    console.error(`❌ LAGO CUSTOMER - Exception creating customer:`, error);
    return null;
  }
}

/**
 * Get an existing customer or create if not found
 * Ensures consistent use of Keycloak ID as external_id
 */
export async function getOrCreateCustomer(customerData: CustomerData): Promise<CustomerData | null> {
  if (!customerData) {
    console.error("❌ LAGO CUSTOMER - Cannot get/create customer: Missing data");
    return null;
  }

  const keycloakId = customerData.external_id;

  if (!keycloakId) {
    console.error("❌ LAGO CUSTOMER - Cannot get/create customer: Missing Keycloak ID");
    return null;
  }

  console.log(`🔍 LAGO CUSTOMER - Getting or creating customer with Keycloak ID: ${keycloakId}`);

  // First check if customer exists
  const existingCustomer = await checkCustomerById(keycloakId);

  if (existingCustomer) {
    console.log(`✅ LAGO CUSTOMER - Found existing customer with ID: ${keycloakId}`);
    return existingCustomer;
  }

  // Not found, create new customer
  console.log(`🔄 LAGO CUSTOMER - Customer not found, creating new one with ID: ${keycloakId}`);
  return await createCustomer(customerData);
}

/**
 * Update a customer's tax identification information in Lago
 * @param keycloakId - The customer's Keycloak ID
 * @param gstNumber - The Tax Identification Number (optional)
 * @param businessName - The legal name (optional)
 * @returns The updated customer object if successful, null otherwise
 */
export async function updateCustomerGstInfo(
  keycloakId: string,
  gstNumber?: string,
  businessName?: string
): Promise<CustomerData | null> {
  console.log(`📝 TAX FLOW - Starting updateCustomerGstInfo with:`, {
    keycloakId,
    taxId: gstNumber || 'Not provided',
    legalName: businessName || 'Not provided'
  });

  if (!keycloakId) {
    console.error("❌ TAX FLOW - Cannot update tax info: Missing Keycloak ID");
    return null;
  }

  // Validate or sanitize ID if possible
  const sanitizedId = sanitizeKeycloakId(keycloakId, "updateCustomerGstInfo");
  const idToUse = sanitizedId || keycloakId;

  if (sanitizedId) {
    console.log(`📝 TAX FLOW - Using sanitized Keycloak ID: ${sanitizedId}`);
  } else {
    console.log(`📝 TAX FLOW - Using original Keycloak ID (not UUID format): ${keycloakId}`);
  }

  // Normalize Tax Identification Number (uppercase and trim)
  const normalizedGstNumber = gstNumber ? gstNumber.trim().toUpperCase() : undefined;

  // Validate Tax Identification Number format if provided
  if (normalizedGstNumber) {
    console.log(`📝 TAX FLOW - Validating Tax Identification Number: ${normalizedGstNumber}`);
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    if (!gstRegex.test(normalizedGstNumber)) {
      console.error(`❌ TAX FLOW - Invalid Tax Identification Number format: ${normalizedGstNumber}`);
      return null;
    }
    console.log(`✅ TAX FLOW - Tax Identification Number format is valid`);
  }

  // Trim legal name if provided
  const trimmedBusinessName = businessName ? businessName.trim() : undefined;

  // Ensure legal name is provided if Tax Identification Number is provided
  if (normalizedGstNumber && !trimmedBusinessName) {
    console.error(`❌ TAX FLOW - Legal name is required when Tax Identification Number is provided`);
    return null;
  }

  console.log(`🔄 TAX FLOW - Updating tax info for customer with ID: ${idToUse}`);
  console.log(`📝 TAX FLOW - Final values being sent to API:`, {
    external_id: idToUse,
    tax_identification_number: normalizedGstNumber || 'Not provided',
    legal_name: trimmedBusinessName || 'Not provided'
  });

  // Prepare update payload
  const payload = {
    customer: {
      external_id: idToUse,
      tax_identification_number: normalizedGstNumber,
      legal_name: trimmedBusinessName
    }
  };

  try {
    // Use our server-side API route that will handle CORS issues
    console.log(`📝 TAX FLOW - Sending request to /api/lago/customer/update`);
    console.log(`📝 TAX FLOW - Payload:`, JSON.stringify(payload));

    const response = await fetch('/api/lago/customer/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    console.log(`🔄 TAX FLOW - Update tax info response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ TAX FLOW - Updated tax info for customer with ID: ${idToUse}`);
      console.log(`📝 TAX FLOW - Response data:`, data);
      return data;
    } else {
      const errorText = await response.text();
      console.error(`❌ TAX FLOW - API error updating tax info: ${response.status}`, errorText);
      try {
        // Try to parse the error as JSON for more details
        const errorJson = JSON.parse(errorText);
        console.error(`❌ TAX FLOW - Error details:`, errorJson);
      } catch {
        // If not JSON, just log the raw text
        console.error(`❌ TAX FLOW - Raw error:`, errorText);
      }
      return null;
    }
  } catch (error) {
    console.error(`❌ TAX FLOW - Exception updating tax info:`, error);
    return null;
  }
}

/**
 * Analyzes a customer's subscriptions to get plan information
 * @param keycloakId Customer ID to check
 * @returns A detailed analysis of all customer plans
 */
export async function analyzeCustomerPlans(keycloakId: string): Promise<{
  hasAnyPlan: boolean;
  hasActivePlan: boolean;
  activePlans: Array<{
    id: string;
    plan_code: string;
    status: string;
    created_at: string;
    next_billing_date?: string;
    product?: string;
    planType?: string;
    planDuration?: string;
    isTrial: boolean;
    displayName: string;
  }>;
}> {
  if (!keycloakId) {
    console.error("❌ LAGO CUSTOMER - Cannot analyze plans: Missing customer ID");
    return {
      hasAnyPlan: false,
      hasActivePlan: false,
      activePlans: []
    };
  }

  try {
    // Get all subscriptions for the customer
    const response = await fetch(`/api/lago/subscription?customerId=${encodeURIComponent(keycloakId)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();

      if (!data.subscriptions || data.subscriptions.length === 0) {
        console.log(`✅ LAGO CUSTOMER - No subscriptions found for customer: ${keycloakId}`);
        return {
          hasAnyPlan: false,
          hasActivePlan: false,
          activePlans: []
        };
      }

      // Import on demand to avoid circular dependencies
      const {
        extractProductCode,
        extractPlanType,
        extractPlanDuration,
        isTrialPlan,
        formatPlanDisplayName
      } = await import('@/src/utils/plan-utils');

      // Process each subscription with more details
      const activePlans = data.subscriptions.map(sub => {
        const productCode = extractProductCode(sub.plan_code);
        const planType = extractPlanType(sub.plan_code);
        const planDuration = extractPlanDuration(sub.plan_code);
        const trial = isTrialPlan(sub.plan_code);
        const displayName = formatPlanDisplayName(sub.plan_code);

        return {
          ...sub,
          product: productCode,
          planType,
          planDuration,
          isTrial: trial,
          displayName
        };
      });

      // Check if there are any active plans
      const hasActivePlan = activePlans.some(plan =>
        plan.status === "active" || plan.status === "pending"
      );

      console.log(`✅ LAGO CUSTOMER - Found ${activePlans.length} plans for customer: ${keycloakId}`);
      console.log(`🔍 LAGO CUSTOMER - Active plans: ${hasActivePlan}`);

      return {
        hasAnyPlan: activePlans.length > 0,
        hasActivePlan,
        activePlans
      };
    } else {
      console.error(`❌ LAGO CUSTOMER - Error fetching subscriptions: ${response.status}`);
      return {
        hasAnyPlan: false,
        hasActivePlan: false,
        activePlans: []
      };
    }
  } catch (error) {
    console.error(`❌ LAGO CUSTOMER - Exception analyzing plans:`, error);
    return {
      hasAnyPlan: false,
      hasActivePlan: false,
      activePlans: []
    };
  }
}

/**
 * Check if customer has any plans for a specific product
 * @param keycloakId Customer ID to check
 * @param productCode Product code to check (e.g. onerestro)
 * @returns Plan information for the product
 */
export async function checkCustomerProductPlans(
  keycloakId: string,
  productCode: string
): Promise<{
  hasActivePlan: boolean;
  hasAnyPlan: boolean;
  planDetails: Array<{
    id: string;
    plan_code: string;
    status: string;
    created_at: string;
    next_billing_date?: string;
    product?: string;
    planType?: string;
    planDuration?: string;
    isTrial: boolean;
    displayName: string;
  }>;
}> {
  if (!keycloakId || !productCode) {
    console.error("❌ LAGO CUSTOMER - Cannot check product plans: Missing customer ID or product code");
    return {
      hasActivePlan: false,
      hasAnyPlan: false,
      planDetails: []
    };
  }

  // Get all plans for the customer
  const plansAnalysis = await analyzeCustomerPlans(keycloakId);

  // Filter for the specific product
  const productPlans = plansAnalysis.activePlans.filter(
    plan => plan.product && plan.product.toLowerCase() === productCode.toLowerCase()
  );

  // Check if there are any active plans for this product
  const hasActivePlan = productPlans.some(
    plan => plan.status === "active" || plan.status === "pending"
  );

  return {
    hasActivePlan,
    hasAnyPlan: productPlans.length > 0,
    planDetails: productPlans
  };
}