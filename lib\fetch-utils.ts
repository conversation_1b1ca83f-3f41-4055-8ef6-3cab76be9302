/**
 * Utility functions for making fetch requests with proper Node.js 18+ compatibility
 * This file is used by both client and server components
 */

/**
 * Makes a fetch request with the proper options for Node.js 18+
 * 
 * @param url The URL to fetch
 * @param options The fetch options
 * @returns The fetch response
 */
export async function safeFetch(url: string, options: RequestInit = {}) {
  // Use this function for all fetch requests to ensure the duplex option is included when needed
  
  // If we have a body in the options, we need to add the duplex option
  if (options.body) {
    // Use Object.assign to bypass TypeScript type checking
    const requestOptions = Object.assign({}, options, {
      duplex: 'half' // Required by Node.js 18+ when sending a body
    });
    
    return fetch(url, requestOptions);
  }
  
  // Otherwise, just use the regular fetch
  return fetch(url, options);
}

/**
 * Makes a POST request with the proper options for Node.js 18+
 * 
 * @param url The URL to fetch
 * @param body The request body (can be any type that fetch accepts)
 * @param options Additional fetch options
 * @returns The fetch response
 */
export async function safePost(url: string, body: any, options: Omit<RequestInit, 'method' | 'body'> = {}) {
  const requestOptions: RequestInit = {
    method: 'POST',
    body: body instanceof FormData || body instanceof URLSearchParams || 
          body instanceof ReadableStream || typeof body === 'string' ? 
          body : JSON.stringify(body),
    ...options
  };
  
  // If headers aren't provided and we're sending JSON, add the content-type header
  if (!options.headers && !(body instanceof FormData) && !(body instanceof URLSearchParams) && 
      !(body instanceof ReadableStream) && typeof body !== 'string') {
    requestOptions.headers = {
      'Content-Type': 'application/json'
    };
  }
  
  return safeFetch(url, requestOptions);
} 