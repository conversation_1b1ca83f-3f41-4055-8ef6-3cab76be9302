"use client";

import Link from "next/link";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export default function DevHomePage() {
  const sections = [
    {
      title: "Test Pages",
      description: "Pages for testing functionality",
      items: [
        {
          title: "Utilities Tests",
          description: "Test various utility functions",
          path: "/dev/tests",
        },
      ]
    },
    {
      title: "Component Demos",
      description: "Interactive demonstrations of components",
      items: [
        {
          title: "Subscription Card",
          description: "Demo of the subscription card component",
          path: "/dev/components/subscription-card",
        },
        {
          title: "Plan Code Display",
          description: "Visualize how plan codes are analyzed and displayed",
          path: "/dev/components/plan-display",
        },
      ]
    },
    {
      title: "Admin Tools",
      description: "Administrative tools for managing the application",
      items: [
        {
          title: "Plan Viewer",
          description: "View customer subscription plans",
          path: "/dev/components/plan-viewer",
        },
      ]
    },
  ];

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-2">Development Portal</h1>
      <p className="text-muted-foreground mb-10">
        This area contains test pages, component demos, and development tools.
      </p>
      
      {sections.map((section, sectionIndex) => (
        <div key={sectionIndex} className="mb-10">
          <h2 className="text-2xl font-bold mb-2">{section.title}</h2>
          <p className="text-muted-foreground mb-6">{section.description}</p>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {section.items.map((item, itemIndex) => (
              <Card key={itemIndex}>
                <CardHeader>
                  <CardTitle>{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button asChild>
                    <Link href={item.path}>Visit</Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
} 