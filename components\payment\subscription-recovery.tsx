"use client";

import { useEffect, useState } from "react";
import { useUser } from "@/context/user-context";
import { createSubscription } from "@/src/services/subscriptionService";
import { toast } from "sonner";

interface FailedSubscriptionAttempt {
  timestamp: number;
  userId: string;
  item: any; // CartItem with planCode
  error: string;
}

/**
 * Silent component that attempts to recover from failed subscription activations
 * It works by checking localStorage for failed attempts and retrying them
 */
export function SubscriptionRecovery() {
  const { userId, userEmail, isLoading } = useUser();
  const [isRecovering, setIsRecovering] = useState(false);

  useEffect(() => {
    // Only run recovery if we have a logged-in user and we're not already recovering
    // and the user context has fully loaded
    if (userId && !isRecovering && !isLoading) {
      const recoverFailedSubscriptions = async () => {
        try {
          setIsRecovering(true);
          
          // Get failed subscription attempts from localStorage
          const failedAttemptsJson = localStorage.getItem("failed_subscription_attempts");
          if (!failedAttemptsJson) return;
          
          const failedAttempts: FailedSubscriptionAttempt[] = JSON.parse(failedAttemptsJson);
          if (!failedAttempts.length) return;
          
          console.log(`Found ${failedAttempts.length} failed subscription attempts, attempting recovery`);
          
          // Filter attempts to only include those for the current user
          const userAttempts = failedAttempts.filter(attempt => attempt.userId === userId);
          if (!userAttempts.length) return;
          
          console.log(`Attempting to recover ${userAttempts.length} failed subscriptions for user ${userId}`);
          
          // Track successful recoveries to remove from failed attempts
          const successfulRecoveries: number[] = [];
          
          // Process each failed attempt
          for (let i = 0; i < userAttempts.length; i++) {
            const attempt = userAttempts[i];
            
            try {
              // Only attempt recovery for items with valid plan codes
              if (attempt.item && attempt.item.planCode) {
                // Attempt to create the subscription
                await createSubscription(
                  userId,
                  attempt.item,
                  userEmail || undefined
                );
                
                // Mark this attempt as successful
                successfulRecoveries.push(i);
                console.log(`Successfully recovered subscription for ${attempt.item.name}`);
                
                // Show success toast (but only show a maximum of one to avoid spamming)
                if (successfulRecoveries.length === 1) {
                  toast.success("Successfully recovered your subscription!");
                }
              }
            } catch (error) {
              console.error(`Recovery attempt failed for ${attempt.item?.name || 'unknown item'}:`, error);
              // Don't show error toast to avoid annoying the user
              // The attempt will remain in localStorage for future recovery
            }
          }
          
          // If we had any successful recoveries, update the failed attempts in localStorage
          if (successfulRecoveries.length > 0) {
            // Remove successful recoveries from the list
            const updatedAttempts = failedAttempts.filter((_, index) => 
              !successfulRecoveries.includes(index)
            );
            
            // Update localStorage
            if (updatedAttempts.length === 0) {
              localStorage.removeItem("failed_subscription_attempts");
            } else {
              localStorage.setItem("failed_subscription_attempts", JSON.stringify(updatedAttempts));
            }
            
            console.log(`Recovery complete. ${successfulRecoveries.length} subscriptions recovered, ${updatedAttempts.length} still pending.`);
          }
        } catch (error) {
          console.error("Error during subscription recovery:", error);
        } finally {
          setIsRecovering(false);
        }
      };
      
      // Run recovery process
      recoverFailedSubscriptions();
    }
  }, [userId, userEmail, isLoading]);

  // This component doesn't render anything
  return null;
} 