import { arePlansFromSameProduct } from "./plan-utils";

/**
 * Check if a user is subscribed to a product
 * @param userId The user's ID
 * @param planCode The plan code to check against
 * @returns Promise with result object containing isSubscribed flag and subscription details if found
 */
export async function checkUserSubscription(userId: string, planCode: string) {
  if (!userId || !planCode) {
    console.error("Missing userId or planCode for subscription check");
    return { isSubscribed: false, subscription: null };
  }

  try {
    // Check for active subscriptions for this product
    const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&status[]=active&status[]=pending`;
    
    console.log(`🔍 SUBSCRIPTION CHECK - Checking active subscriptions for user ${userId} for plan ${planCode}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
      }
    });
    
    if (!response.ok) {
      console.error(`❌ SUBSCRIPTION CHECK - Error checking subscriptions: ${response.status}`);
      return { isSubscribed: false, subscription: null };
    }
    
    const data = await response.json();
    const subscriptions = data.subscriptions || [];
    
    console.log(`✅ SUBSCRIPTION CHECK - Found ${subscriptions.length} total subscriptions for user ${userId}`);
    
    // Check if any subscription is for the same product as the plan code
    const matchingSubscription = subscriptions.find(sub => 
      arePlansFromSameProduct(sub.plan_code, planCode)
    );
    
    if (matchingSubscription) {
      console.log(`⚠️ SUBSCRIPTION CHECK - User already has a subscription for the same product as ${planCode}: ${matchingSubscription.plan_code}`);
      
      return { 
        isSubscribed: true, 
        subscription: {
          isPaidPlan: !matchingSubscription.plan_code.includes('trial'),
          isTrialPlan: matchingSubscription.plan_code.includes('trial'),
          planName: matchingSubscription.name || matchingSubscription.plan_code,
          planExpiration: matchingSubscription.ending_at,
          isActive: matchingSubscription.status === 'active',
          isExpired: false,
          isPending: matchingSubscription.status === 'pending',
          daysRemaining: matchingSubscription.ending_at ? 
            Math.ceil((new Date(matchingSubscription.ending_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 
            undefined
        }
      };
    }
    
    return { isSubscribed: false, subscription: null };
  } catch (error) {
    console.error("❌ SUBSCRIPTION CHECK - Error checking subscription:", error);
    return { isSubscribed: false, subscription: null };
  }
}

/**
 * Check if a cart already contains a plan for the same product
 * @param cartItems Array of cart items
 * @param planCode The plan code to check against
 * @returns Boolean indicating if the cart contains a plan for the same product
 */
export function isProductInCart(cartItems: any[], planCode: string) {
  if (!cartItems || !planCode) return false;
  
  return cartItems.some(item => 
    item.planCode && arePlansFromSameProduct(item.planCode, planCode)
  );
}
