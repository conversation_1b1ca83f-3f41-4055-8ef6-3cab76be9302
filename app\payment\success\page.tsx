'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CheckCircle2, Loader2 } from 'lucide-react';
import { useUser } from '@/context/user-context';
import { createSubscription, SubscriptionResponse, CartItem } from '@/src/services/lago/subscriptionService';
import { getLatestInvoiceByCustomerId, updateInvoicePaymentStatus } from '@/src/services/lago/invoiceService';
import { toast } from 'sonner';
import { PendingInvoiceSection } from '@/components/payment/pending-invoice-section';
import { AutoPaymentStatusUpdater } from '@/components/payment/auto-payment-status-updater';

interface PaymentDetailsState {
  timestamp: string;
  items?: Array<CartItem>;
  status: 'idle' | 'processing' | 'success' | 'error';
  paymentId?: string;
}

// Type for the stored cart items that might be missing some required fields
interface StoredCartItem {
  id?: string;
  name?: string;
  planCode?: string;
  [key: string]: unknown;
}

export default function PaymentSuccessPage() {
  const { userId, userEmail, isLoading } = useUser();

  const [paymentDetails, setPaymentDetails] = useState<PaymentDetailsState>({
    timestamp: '',
    items: [],
    status: 'idle'
  });

  // Helper for structured logging
  const logPaymentProcess = (step: string, data?: unknown) => {
    console.log(`🔄 PAYMENT SUCCESS [${new Date().toISOString()}] - ${step}`, data || '');
  };

  useEffect(() => {
    // Try to get payment details from localStorage
    try {
      logPaymentProcess('Checking for payment details in localStorage');

      const timestamp = localStorage.getItem('payment_success_timestamp');
      const itemsJson = localStorage.getItem('payment_cart_items');
      const paymentStatus = localStorage.getItem('payment_status');
      const paymentId = localStorage.getItem('last_payment_id');

      // Log all the values for debugging
      logPaymentProcess('Payment details from localStorage', {
        timestamp: timestamp ? 'present' : 'missing',
        itemsJson: itemsJson ? 'present' : 'missing',
        paymentStatus,
        paymentId,
        userId: userId || 'missing',
        isLoading
      });

      if (timestamp) {
        const parsedItems: StoredCartItem[] = itemsJson ? JSON.parse(itemsJson) : [];

        // Ensure each item has an id field
        const items = parsedItems.map((item: StoredCartItem) => ({
          id: item.id || `item-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          name: item.name || 'Subscription',
          planCode: item.planCode,
          ...item
        })) as CartItem[];

        logPaymentProcess(`Found ${items.length} items in cart`);

        setPaymentDetails({
          timestamp: new Date(parseInt(timestamp)).toLocaleString(),
          items,
          status: 'idle',
          paymentId: paymentId || undefined
        });

        if (paymentStatus === 'succeeded' && userId && !isLoading) {
          logPaymentProcess('Payment success detected, initiating subscription flow');
          handleSuccessfulPayment();
        } else {
          logPaymentProcess('Not initiating subscription flow', {
            paymentStatus,
            hasUserId: !!userId,
            isLoading
          });
        }
      } else {
        logPaymentProcess('No payment timestamp found in localStorage');
      }
    } catch (error) {
      console.error('❌ PAYMENT SUCCESS - Error reading payment details:', error);
    }
  }, [userId, isLoading]);

  const handleSuccessfulPayment = async () => {
    if (paymentDetails.status === 'processing' || paymentDetails.status === 'success') {
      return;
    }

    setPaymentDetails(prev => ({ ...prev, status: 'processing' }));
    logPaymentProcess('Starting subscription and invoice flow');

    // Validate user ID (Keycloak ID)
    if (!userId) {
      logPaymentProcess('No user ID found, aborting');
      setPaymentDetails(prev => ({ ...prev, status: 'error' }));
      toast.error('User identification failed. Please contact support.');
      return;
    }

    try {
      // Step 1: Create subscription in Lago
      logPaymentProcess('Step 1: Creating subscription in Lago', { userId, items: paymentDetails.items });

      let latestSubscriptionResult: SubscriptionResponse | null = null;

      // Process each item in the cart
      if (paymentDetails.items && paymentDetails.items.length > 0) {
        for (const item of paymentDetails.items) {
          if (!item.planCode) {
            logPaymentProcess(`Item ${item.name} has no plan code, skipping`);
            continue;
          }

          logPaymentProcess(`Creating subscription for ${item.name}`, { planCode: item.planCode });

          const subscriptionResult = await createSubscription(userId, item, userEmail || undefined);

          if (subscriptionResult?.subscription) {
            logPaymentProcess('Subscription created successfully', {
              id: subscriptionResult.subscription.id,
              planCode: subscriptionResult.subscription.plan_code
            });
            latestSubscriptionResult = subscriptionResult;
          } else {
            logPaymentProcess(`Failed to create subscription for ${item.name}`, { error: subscriptionResult?.error });
          }
        }
      } else {
        logPaymentProcess('No items found in cart, skipping subscription creation');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        toast.error('No subscription items found. Please contact support.');
        return;
      }

      if (!latestSubscriptionResult?.subscription) {
        logPaymentProcess('No subscriptions were created successfully');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        toast.error('Failed to create subscription. Please contact support.');
        return;
      }

      // Step 2: Wait briefly for invoice generation
      logPaymentProcess('Step 2: Waiting for invoice generation');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Step 3: Fetch the invoice
      const subscriptionDate = latestSubscriptionResult.subscription.created_at;
      const formattedDate = new Date(subscriptionDate).toISOString().split('T')[0];

      logPaymentProcess('Step 3: Fetching invoice details', {
        external_customer_id: userId,
        issuing_date_from: formattedDate
      });

      const invoice = await getLatestInvoiceByCustomerId(userId, formattedDate);

      if (!invoice) {
        logPaymentProcess('No invoice found, waiting and retrying');
        // Try one more time after a delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        const retryInvoice = await getLatestInvoiceByCustomerId(userId, formattedDate);

        if (!retryInvoice) {
          logPaymentProcess('Invoice not found after retry');
          setPaymentDetails(prev => ({ ...prev, status: 'error' }));
          toast.error('Invoice not found. Subscription created but payment status not updated.');
          return;
        }

        logPaymentProcess('Invoice found after retry', { lagoId: retryInvoice.lago_id });
      }

      const invoiceToUpdate = invoice || await getLatestInvoiceByCustomerId(userId, formattedDate);

      if (!invoiceToUpdate) {
        logPaymentProcess('Could not find any invoice to update');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        return;
      }

      // Step 4: Update the payment status
      logPaymentProcess('Step 4: Updating payment status', {
        lagoId: invoiceToUpdate.lago_id,
        current_status: invoiceToUpdate.payment_status
      });

      const updateResult = await updateInvoicePaymentStatus(invoiceToUpdate.lago_id);

      if (updateResult) {
        logPaymentProcess('Payment status updated successfully to "succeeded"');
        setPaymentDetails(prev => ({ ...prev, status: 'success' }));
        toast.success('Payment completed successfully!');
      } else {
        logPaymentProcess('Failed to update payment status');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        toast.error('Failed to update payment status. Please contact support.');
      }

    } catch (error) {
      console.error('❌ PAYMENT SUCCESS - Error in payment flow:', error);
      setPaymentDetails(prev => ({ ...prev, status: 'error' }));
      toast.error('An error occurred during payment processing. Please contact support.');
    }
  };

  // Get the date from the subscription creation time for pending invoice check
  const getDateFromForInvoices = () => {
    if (paymentDetails.timestamp) {
      try {
        // Try to parse the timestamp from the displayed format
        const date = new Date(paymentDetails.timestamp);
        if (!isNaN(date.getTime())) {
          // If valid date, return in YYYY-MM-DD format
          return date.toISOString().split('T')[0];
        }
      } catch (e) {
        console.error('Error parsing timestamp:', e);
      }
    }
    // Fallback to current date
    return new Date().toISOString().split('T')[0];
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-4">
      <div className="max-w-3xl w-full mx-auto">
        <div className="bg-white p-8 rounded-lg shadow-lg mb-6 text-center">
          <div className="flex justify-center mb-6">
            {paymentDetails.status === 'processing' ? (
              <Loader2 size={64} className="text-blue-500 animate-spin" />
            ) : (
              <CheckCircle2 size={64} className="text-green-500" />
            )}
          </div>

          <h1 className="text-2xl font-bold mb-2">Payment Successful!</h1>
          <p className="text-gray-600 mb-6">
            Thank you for your payment. {paymentDetails.status === 'success'
              ? 'Your subscription has been activated.'
              : paymentDetails.status === 'processing'
                ? 'We are activating your subscription...'
                : paymentDetails.status === 'error'
                  ? 'There was an issue activating your subscription. Please contact support.'
                  : 'Your subscription is being activated.'}
          </p>

          {paymentDetails.timestamp && (
            <div className="mb-6 text-sm text-gray-600">
              <p className="mb-2">Payment processed: {paymentDetails.timestamp}</p>

              {paymentDetails.items && paymentDetails.items.length > 0 && (
                <div className="mt-4">
                  <p className="font-semibold mb-1">Subscription details:</p>
                  <ul className="text-left pl-4 list-disc inline-block">
                    {paymentDetails.items.map((item, index) => (
                      <li key={index}>{item.name || item.planCode}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="flex-1">
              <Link href={`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`}>
                Go to Dashboard
              </Link>
            </Button>

            <Button variant="outline" asChild className="flex-1">
              <Link href="/support">
                Need help?
              </Link>
            </Button>
          </div>
        </div>

        {/* Pending Invoice Section */}
        {userId && paymentDetails.status === 'success' && (
          <PendingInvoiceSection
            userId={userId}
            dateFrom={getDateFromForInvoices()}
            autoProcess={true}
            className="bg-white shadow-lg"
          />
        )}

        {/* Auto Payment Status Updater - invisible component that automatically updates payment status */}
        {userId && (
          <AutoPaymentStatusUpdater
            dateFrom={getDateFromForInvoices()}
            enabled={true}
            paymentId={paymentDetails.paymentId}
          />
        )}
      </div>
    </div>
  );
}