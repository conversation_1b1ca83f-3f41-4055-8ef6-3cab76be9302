"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { TrialFlow } from "@/components/trial/trial-flow";
import { TrialStatusDialog } from "@/components/trial/trial-status-dialog";
import { SubscriptionInfoDialog } from "@/components/trial/subscription-info-dialog";
import { useSearchParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { useSession, signIn } from "next-auth/react";
import {
  hasUserUsedTrial,
  hasUserUsedTrialForProduct,
  checkProductSubscription,
  ensureUserExists
} from "@/src/services/trialService";
import { useUser } from "@/context/user-context";
import { TrialPlan, SubscriptionStatus } from "@/src/constants/types";
import { extractProductCode } from "@/src/utils/plan-utils";

interface TrialButtonProps {
  buttonText: string;
  productSlug: string;
  productName?: string;
  trialPlan?: TrialPlan;
  className?: string;
  onScrollToPricing?: () => void;
}

export function TrialButton({
  buttonText,
  productSlug,
  productName = "",
  trialPlan,
  className,
  onScrollToPricing,
}: TrialButtonProps) {
  const [isTrialFlowOpen, setIsTrialFlowOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isSubscriptionInfoOpen, setIsSubscriptionInfoOpen] = useState(false);
  const [hasUsedTrial, setHasUsedTrial] = useState(false);
  const [isPendingTrial, setIsPendingTrial] = useState(false);
  const [trialStatus, setTrialStatus] = useState<string | undefined>(undefined);
  const [isCheckingTrial, setIsCheckingTrial] = useState(false);
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(false);
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [existingSubscription, setExistingSubscription] = useState<{
    isPaidPlan: boolean;
    isTrialPlan: boolean;
    planName?: string;
    planExpiration?: string;
    isActive: boolean;
    isExpired: boolean;
    isPending?: boolean;
    daysRemaining?: number;
  } | null>(null);

  const searchParams = useSearchParams();
  const { status } = useSession();
  const { userId, userEmail, userName, isLoading: userContextLoading } = useUser();
  const router = useRouter();
  const productCodeRef = useRef<string>();

  // Extract product code from the trial plan code
  useEffect(() => {
    if (trialPlan?.plan_code) {
      // Extract product code from the plan code
      // Example: "product_trial" -> "product"
      const codeMatch = trialPlan.plan_code.match(/^([a-z0-9_]+?)_/i);
      if (codeMatch && codeMatch[1]) {
        productCodeRef.current = codeMatch[1];
      } else {
        // Fallback to using the product slug as the code
        productCodeRef.current = productSlug;
      }
    }
  }, [trialPlan, productSlug]);

  // Check if the user was redirected back after authentication
  useEffect(() => {
    // If the URL has a trial=true parameter, automatically open trial flow
    const trialParam = searchParams.get("trial");
    if (trialParam === "true") {
      // Check if we have the saved trial data
      const savedProductSlug = localStorage.getItem("trial_product_slug");

      // Only open if the saved slug matches current product
      if (savedProductSlug === productSlug) {
        setIsTrialFlowOpen(true);
        // Clean URL to avoid reopening on refresh
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  }, [searchParams, productSlug]);

  // Check existing subscriptions when user is authenticated
  useEffect(() => {
    const checkExistingSubscription = async () => {
      if (status !== "authenticated" || !userId || !productSlug) return;

      setIsCheckingSubscription(true);
      try {
        // Use direct subscription checking for more accurate results
        const subscription = await checkProductSubscription(
          userId,
          productSlug,
          trialPlan?.plan_code || ''
        );

        if (subscription && subscription.hasPlan) {
          // User has a subscription for this product
          setExistingSubscription({
            isPaidPlan: subscription.isPaidPlan,
            isTrialPlan: subscription.isTrialPlan,
            planName: subscription.planName,
            planExpiration: subscription.planExpiration,
            isActive: subscription.isActive,
            isExpired: subscription.isExpired,
            isPending: subscription.isPending,
            daysRemaining: subscription.daysRemaining
          });

          // If user has a subscription, no need to check for trial usage
          setIsCheckingTrial(false);
          return;
        } else {
          setExistingSubscription(null);
        }
      } catch (error) {
        console.error("Error checking subscriptions:", error);
        setExistingSubscription(null);
      } finally {
        setIsCheckingSubscription(false);
      }

      // If no subscription found, check if they've used the trial
      checkTrialStatus();
    };

    checkExistingSubscription();
  }, [status, userId, productSlug, trialPlan?.plan_code]);

  // Check if user has already used the trial
  const checkTrialStatus = async () => {
    if (status !== "authenticated" || !userId || !productSlug) return;

    setIsCheckingTrial(true);
    try {
      // Check if user has used trial for this product using new function
      const productTrialResult = await hasUserUsedTrialForProduct(userId, productSlug);
      setHasUsedTrial(productTrialResult.hasUsedTrial);
      setIsPendingTrial(productTrialResult.isPending);

      if (productTrialResult.hasUsedTrial) {
        // Store the trial status message for display
        const statusMessage = productTrialResult.message ||
          `You have already used a trial for ${productSlug}`;

        // Set the trial status message
        setTrialStatus(statusMessage);

        // Log more detailed information for debugging
        console.log(`✅ TRIAL BUTTON - User has used trial for product ${productSlug}: isPending=${productTrialResult.isPending}, message=${statusMessage}`);

        // Check if this is a product family scenario
        if (productTrialResult.relatedToProductFamily) {
          // If it's about a product family, this is an important note to display
          toast.info(statusMessage, {
            duration: 5000,
            position: "bottom-center"
          });
        }

        // No need to check plan-specific status if we already know from product family
        return;
      }

      // Only check the specific plan if product family check didn't find any usage
      if (trialPlan?.plan_code) {
        // Fallback to direct plan check if product check doesn't find a used trial
        const planTrialResult = await hasUserUsedTrial(userId, trialPlan.plan_code);

        // Only update if product check didn't already find a used trial
        if (planTrialResult.hasUsed) {
          setHasUsedTrial(planTrialResult.hasUsed);
          setIsPendingTrial(planTrialResult.isPending);
          setTrialStatus(planTrialResult.status);
          console.log(`✅ TRIAL BUTTON - Trial status for plan ${trialPlan.plan_code}: hasUsed=${planTrialResult.hasUsed}, isPending=${planTrialResult.isPending}, status=${planTrialResult.status}`);
        }
      }
    } catch (error) {
      console.error("Error checking trial status:", error);
      // Don't show error toast here as it's just a background check
    } finally {
      setIsCheckingTrial(false);
    }
  };

  // Handle button click
  const handleTrialClick = async () => {
    if (!productSlug) {
      toast.error("Product information is not available");
      return;
    }

    // If the user already has a subscription, show the subscription info dialog
    if (existingSubscription) {
      console.log(`🔍 TRIAL BUTTON - User has existing subscription, showing subscription info dialog`);
      setIsSubscriptionInfoOpen(true);
      return;
    }

    // If the user has used a trial but doesn't have an active subscription,
    // show the trial status dialog
    if (hasUsedTrial) {
      console.log(`🔍 TRIAL BUTTON - User has used trial, showing trial status dialog`);
      setIsStatusDialogOpen(true);
      return;
    }

    // If user context is still loading, wait
    if (userContextLoading) {
      console.log(`🔍 TRIAL BUTTON - User context still loading, showing loading message`);
      toast.info("Loading user information...");
      return;
    }

    // If user is not authenticated, trigger sign in
    if (status !== "authenticated") {
      console.log(`🔍 TRIAL BUTTON - User not authenticated, redirecting to sign in`);
      try {
        localStorage.setItem("trial_product_slug", productSlug);
        if (trialPlan?.plan_code) {
          localStorage.setItem("trial_plan_code", trialPlan.plan_code);
        }
        signIn("keycloak", { callbackUrl: `/products/${productSlug}?trial=true` });
      } catch (error) {
        console.error("Error storing trial data:", error);
        toast.error("Failed to store trial data. Please try again.");
      }
      return;
    }

    // If authenticated but no userId, show error (only after user context has finished loading)
    if (!userId) {
      console.error(`❌ TRIAL BUTTON - User authenticated but no userId available`);
      toast.error("User information not available. Please try signing out and back in.");
      return;
    }

    console.log(`✅ TRIAL BUTTON - All checks passed, proceeding with trial for user: ${userId}`);

    // Check/create user in Lago before proceeding
    setIsCreatingUser(true);
    try {
      const userExists = await ensureUserExists(
        userId,
        userEmail || undefined,
        userName || undefined
      );
      if (!userExists) {
        toast.error("Unable to create user account. Please try again later.");
        return;
      }

      // Check if user has any active subscriptions for this product
      // This is a direct check using the new endpoint format
      if (trialPlan?.plan_code) {
        const productCode = extractProductCode(trialPlan.plan_code);
        const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&status[]=active&status[]=pending`;

        console.log(`🔍 TRIAL BUTTON - Direct check for active subscriptions for user ${userId} and product ${productCode}`);

        try {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
            }
          });

          if (response.ok) {
            const data = await response.json();
            const subscriptions = data.subscriptions || [];

            // Filter for subscriptions of this product
            const productSubscriptions = subscriptions.filter((sub: any) => {
              const subProductCode = extractProductCode(sub.plan_code);
              return subProductCode === productCode;
            });

            if (productSubscriptions.length > 0) {
              console.log(`⚠️ TRIAL BUTTON - Direct check found ${productSubscriptions.length} active/pending subscriptions for product ${productCode}`);

              // Update the subscription state
              const subscription = productSubscriptions[0];
              setExistingSubscription({
                isPaidPlan: !subscription.plan_code.includes('trial'),
                isTrialPlan: subscription.plan_code.includes('trial'),
                planName: subscription.name || subscription.plan_code,
                planExpiration: subscription.ending_at,
                isActive: subscription.status === 'active',
                isExpired: false,
                isPending: subscription.status === 'pending',
                daysRemaining: subscription.ending_at ? Math.ceil((new Date(subscription.ending_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : undefined
              });

              // Show the subscription info dialog
              setIsSubscriptionInfoOpen(true);
              setIsCreatingUser(false);
              return;
            }
          }
        } catch (error) {
          console.error("Error in direct subscription check:", error);
          // Continue with the regular flow if this check fails
        }
      }

      // Perform complete eligibility check for the product
      const trialResult = await hasUserUsedTrialForProduct(userId, productSlug);
      setHasUsedTrial(trialResult.hasUsedTrial);
      setIsPendingTrial(trialResult.isPending);

      console.log(`✅ TRIAL BUTTON - Updated trial status for product: hasUsed=${trialResult.hasUsedTrial}, isPending=${trialResult.isPending}`);

      // If trial is not eligible, show why with a more detailed message
      if (trialResult.hasUsedTrial) {
        const message = trialResult.message || "You are not eligible for this trial.";

        // For pending trials, use info toast
        if (trialResult.isPending) {
          toast.info(message, { duration: 5000 });
        } else {
          // For any other ineligibility, use warning toast
          toast.warning(message, { duration: 5000 });
        }

        // For ineligible users, potentially redirect them to pricing instead of the trial flow
        if (message.includes("paid plan") && onScrollToPricing) {
          // If they already have a paid plan, just scroll to pricing
          setTimeout(() => {
            onScrollToPricing();
          }, 1000);
          return;
        }
      }
    } catch (error) {
      console.error("Error checking/creating user:", error);
      toast.error("Unable to verify your account. Please try again later.");
      return;
    } finally {
      setIsCreatingUser(false);
    }

    // Show status dialog to check eligibility
    setIsStatusDialogOpen(true);
  };

  // Handle scroll to pricing section
  const handleViewPricing = () => {
    setIsStatusDialogOpen(false);
    setIsSubscriptionInfoOpen(false);
    if (onScrollToPricing) {
      setTimeout(() => {
        onScrollToPricing();
      }, 100);
    }
  };

  // Handle go to dashboard
  const handleGoToDashboard = () => {
    setIsStatusDialogOpen(false);
    setIsSubscriptionInfoOpen(false);
    router.push(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`);
  };

  // Handle starting the trial
  const handleStartTrial = () => {
    setIsStatusDialogOpen(false);
    setIsTrialFlowOpen(true);
  };

  // Determine button text to display based on status
  const getButtonText = () => {
    if (userContextLoading) {
      return "Loading...";
    }

    if (isCreatingUser) {
      return "Setting up...";
    }

    if (isCheckingTrial || isCheckingSubscription) {
      return "Checking...";
    }

    if (existingSubscription) {
      if (existingSubscription.isTrialPlan) {
        if (existingSubscription.isPending) {
          return "Trial Pending";
        }
        if (existingSubscription.isExpired) {
          return "Upgrade Now";
        }
        return "View Trial Status";
      }

      if (existingSubscription.isExpired) {
        return "Renew Subscription";
      }
      return "View Subscription";
    }

    if (isPendingTrial) {
      return "Trial Pending";
    }

    if (hasUsedTrial) {
      return "View Plans";
    }

    return buttonText || "Start Free Trial";
  };

  // Determine button variant based on subscription status
  const getButtonVariant = (): "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" => {
    if (existingSubscription) {
      if (existingSubscription.isTrialPlan) {
        if (existingSubscription.isExpired) {
          return "default";
        }
        return "secondary";
      }

      if (existingSubscription.isExpired) {
        return "default";
      }
      return "outline";
    }

    if (hasUsedTrial) {
      return "secondary";
    }

    return "destructive";
  };

  // Create a status object for the trial status dialog
  const subscriptionStatus: SubscriptionStatus = {
    isLoading: isCheckingTrial || isCheckingSubscription || isCreatingUser,
    hasActiveSubscription: !!existingSubscription?.isActive,
    hasExpiredSubscription: !!existingSubscription?.isExpired,
    hasPendingSubscription: !!existingSubscription?.isPending,
    hasUsedTrial,
    trialStatus,
    subscriptionDetails: existingSubscription ? {
      planName: existingSubscription.planName || 'Subscription',
      isPaidPlan: existingSubscription.isPaidPlan,
      isTrialPlan: existingSubscription.isTrialPlan,
      isPending: existingSubscription.isPending,
      daysRemaining: existingSubscription.daysRemaining,
      expirationDate: existingSubscription.planExpiration
    } : undefined
  };

  return (
    <>
      <Button
        onClick={handleTrialClick}
        className={className}
        variant={getButtonVariant()}
        disabled={userContextLoading || isCheckingTrial || isCheckingSubscription || isCreatingUser || isPendingTrial}
      >
        {getButtonText()}
      </Button>

      <TrialStatusDialog
        open={isStatusDialogOpen}
        onClose={() => setIsStatusDialogOpen(false)}
        productName={productName || trialPlan?.name || "this product"}
        status={subscriptionStatus}
        onViewPricing={handleViewPricing}
        onStartTrial={handleStartTrial}
        onGoToDashboard={handleGoToDashboard}
      />

      {trialPlan && (
        <TrialFlow
          open={isTrialFlowOpen}
          onClose={() => setIsTrialFlowOpen(false)}
          productSlug={productSlug}
          trialPlan={trialPlan}
        />
      )}

      {existingSubscription && (
        <SubscriptionInfoDialog
          open={isSubscriptionInfoOpen}
          onClose={() => setIsSubscriptionInfoOpen(false)}
          productName={productName || trialPlan?.name || "this product"}
          productSlug={productSlug}
          currentPlan={existingSubscription}
          onViewPricing={handleViewPricing}
          onGoToDashboard={handleGoToDashboard}
        />
      )}
    </>
  );
}