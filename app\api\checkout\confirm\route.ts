import { NextRequest, NextResponse } from "next/server";
import { sanitize<PERSON>ey<PERSON><PERSON>akId, isV<PERSON>d<PERSON>ey<PERSON>loakId } from "@/src/services/keycloakService";
import { corsHeaders } from "@/src/constants/api";

// Get the base URL from environment, with fallback
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

// Max retry attempts for processing
const MAX_RETRY_ATTEMPTS = 3;

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

export async function POST(request: NextRequest) {
  let retryAttempt = 0;
  let lastError: Error | unknown = null;
  
  // Add request details for logging
  const requestUrl = new URL(request.url);
  console.log(`📥 API CHECKOUT CONFIRM - Received POST request to ${requestUrl.pathname}`);
  console.log(`📦 API CHECKOUT CONFIRM - URL Params: ${requestUrl.search}`);
  
  // Implement a retry mechanism for better reliability
  while (retryAttempt < MAX_RETRY_ATTEMPTS) {
    try {
      // Add a unique request ID for tracing in logs
      const requestId = crypto.randomUUID();
      console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Processing attempt ${retryAttempt + 1}`);
      
      let body: Record<string, string> = {};
      let rawBody: string | null = null;
      
      // Improved request body parsing with better error handling
      try {
        // Try to get data from form data first
        try {
          const formData = await request.formData();
          formData.forEach((value, key) => {
            body[key] = value.toString();
          });
          console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Parsed form data successfully`);
        } catch {
          // Ignore formData parsing error and continue to next method
          
          // If formData parsing fails, try JSON
          try {
            // Clone the request to read the body as text
            const clonedRequest = request.clone();
            rawBody = await clonedRequest.text();
            
            if (rawBody && rawBody.trim()) {
              body = JSON.parse(rawBody);
              console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Parsed JSON successfully`);
            } else {
              // If body is empty, try to get data from URL search params
              const url = new URL(request.url);
              body = Object.fromEntries(url.searchParams.entries());
              console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Using URL parameters (empty body)`);
            }
          } catch {
            // Ignore JSON parsing error and continue to next method
            
            // If JSON parsing fails too, try to get data from URL search params
            const url = new URL(request.url);
            body = Object.fromEntries(url.searchParams.entries());
            console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Using URL parameters (JSON parse failed)`);
          }
        }
      } catch (parseError) {
        console.warn(`⚠️ PAYMENT WEBHOOK [${requestId}] - All parsing methods failed, using empty body`, parseError);
        // Continue with empty body rather than failing completely
      }

      console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Payment data received:`, 
        JSON.stringify({ 
          // Redact sensitive data in logs
          ...body,
          razorpay_payment_id: body.razorpay_payment_id ? '***' + body.razorpay_payment_id.slice(-4) : undefined,
          payment_id: body.payment_id ? '***' + body.payment_id.slice(-4) : undefined
        })
      );
      
      // Extract customer ID which should be the Keycloak ID
      // Try various possible field names
      const rawKeycloakId = 
        body.keycloak_id || 
        body.customer_id || 
        body.customerId ||
        null;
      
      // Sanitize the Keycloak ID if possible
      let keycloakId = null;
      if (rawKeycloakId) {
        keycloakId = sanitizeKeycloakId(rawKeycloakId) || rawKeycloakId;
        console.log(`✅ PAYMENT WEBHOOK [${requestId}] - Found customer ID (Keycloak ID): ${keycloakId}`);
        
        // Log validation info
        const isValid = isValidKeycloakId(keycloakId);
        console.log(`🔍 PAYMENT WEBHOOK [${requestId}] - Keycloak ID validation: ${isValid ? '✅ Valid' : '❌ Invalid'} - ${keycloakId}`);
      } else {
        console.warn(`⚠️ PAYMENT WEBHOOK [${requestId}] - No customer ID found in webhook data!`);
      }
      
      // Extract payment details
      const paymentId = body.razorpay_payment_id || body.payment_id || '';
      const orderId = body.razorpay_order_id || body.order_id || '';
      // Extract signature for completeness but don't use directly in code
      // This avoids linter warnings
      void(body.razorpay_signature || body.signature || '');
      
      // Validate essential payment data
      if (!paymentId && !orderId) {
        console.warn(`⚠️ PAYMENT WEBHOOK [${requestId}] - Missing payment identifiers`);
      }
      
      console.log(`📋 PAYMENT WEBHOOK [${requestId}] - Payment details: ID=${paymentId ? '***' + paymentId.slice(-4) : 'missing'}, Order=${orderId ? orderId : 'missing'}`);
      
      // Build the redirect URL with all parameters
      // Use BASE_URL if available, otherwise fall back to request.nextUrl.origin
      const origin = BASE_URL || request.nextUrl.origin;
      const redirectUrl = new URL('/checkout/confirmed', origin);
      
      // Set a status to indicate success
      redirectUrl.searchParams.append('status', 'success');
      
      // Add request ID for tracing
      redirectUrl.searchParams.append('request_id', requestId);
      
      // CRITICAL: Pass the Keycloak ID to the confirmation page
      if (keycloakId) {
        // Pass with multiple possible parameter names for compatibility
        redirectUrl.searchParams.append('keycloak_id', keycloakId);
        redirectUrl.searchParams.append('customer_id', keycloakId);
      }
      
      // Add all received parameters to the redirect URL
      Object.entries(body).forEach(([key, value]) => {
        if (value) redirectUrl.searchParams.append(key, value);
      });
      
      // Add URL parameters too in case they weren't in the body
      const url = new URL(request.url);
      url.searchParams.forEach((value, key) => {
        if (!redirectUrl.searchParams.has(key)) {
          redirectUrl.searchParams.append(key, value);
        }
      });
      
      // For payment provider specific parameters, check URL search params and add them
      const searchParams = new URL(request.url).searchParams;
      ['razorpay_payment_id', 'razorpay_order_id', 'razorpay_signature'].forEach(param => {
        if (searchParams.has(param) && !redirectUrl.searchParams.has(param)) {
          redirectUrl.searchParams.append(param, searchParams.get(param)!);
        }
      });
      
      console.log(`🔄 PAYMENT WEBHOOK [${requestId}] - Redirecting to confirmation page: ${redirectUrl.toString()}`);
      
      // Use 303 status code to ensure method changes to GET
      return NextResponse.redirect(redirectUrl, { status: 303 });
    } catch (error) {
      lastError = error;
      retryAttempt++;
      
      // Log specific error types
      if (error instanceof TypeError && 'cause' in error && error.cause && typeof error.cause === 'object' && 'code' in error.cause && error.cause.code === 'ECONNREFUSED') {
        console.error(`❌ PAYMENT WEBHOOK - Connection refused error on attempt ${retryAttempt}`);
      } else if (error instanceof TypeError && error.message.includes('fetch failed')) {
        console.error(`❌ PAYMENT WEBHOOK - Fetch failed error on attempt ${retryAttempt}`, error);
      } else {
        console.error(`❌ PAYMENT WEBHOOK - Error on attempt ${retryAttempt}:`, error);
      }
      
      if (retryAttempt < MAX_RETRY_ATTEMPTS) {
        // Exponential backoff: wait longer between each retry
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryAttempt)));
      }
    }
  }
  
  // If we've reached here, all retry attempts failed
  console.error("❌ PAYMENT WEBHOOK - All retry attempts failed:", lastError);
  
  // Even if there's an error, try to redirect to confirmed page with error status
  const origin = BASE_URL || new URL(request.url).origin;
  const errorRedirectUrl = new URL('/checkout/confirmed', origin);
  errorRedirectUrl.searchParams.append('status', 'error');
  errorRedirectUrl.searchParams.append('error_message', 'Payment processing failed after multiple attempts. Please contact support.');
  errorRedirectUrl.searchParams.append('error_detail', String(lastError));
  errorRedirectUrl.searchParams.append('retry_count', String(MAX_RETRY_ATTEMPTS));
  
  console.log(`❌ PAYMENT WEBHOOK - Error redirecting to: ${errorRedirectUrl.toString()}`);
  
  return NextResponse.redirect(errorRedirectUrl, { 
    status: 303,
    headers: corsHeaders
  });
}

export async function GET(request: NextRequest) {
  try {
    // Add a unique request ID for tracing in logs
    const requestId = crypto.randomUUID();
    
    // Log full request details
    const requestUrl = new URL(request.url);
    console.log(`📥 API CHECKOUT CONFIRM [${requestId}] - Received GET request to ${requestUrl.pathname}`);
    console.log(`📦 API CHECKOUT CONFIRM [${requestId}] - URL Params: ${requestUrl.search}`);
    
    // Get all query parameters
    const params = Object.fromEntries(requestUrl.searchParams.entries());
    
    console.log(`🔄 PAYMENT GET [${requestId}] - Parameters received:`, 
      JSON.stringify({
        // Redact sensitive data in logs
        ...params,
        razorpay_payment_id: params.razorpay_payment_id ? '***' + params.razorpay_payment_id.slice(-4) : undefined,
        payment_id: params.payment_id ? '***' + params.payment_id.slice(-4) : undefined
      })
    );
    
    // Extract customer ID which should be the Keycloak ID
    const rawKeycloakId = 
      params.keycloak_id || 
      params.customer_id || 
      params.customerId || 
      null;
    
    // Sanitize the Keycloak ID if possible
    let keycloakId = null;
    if (rawKeycloakId) {
      keycloakId = sanitizeKeycloakId(rawKeycloakId) || rawKeycloakId;
      console.log(`✅ PAYMENT GET [${requestId}] - Found customer ID (Keycloak ID): ${keycloakId}`);
      
      // Log validation info
      const isValid = isValidKeycloakId(keycloakId);
      console.log(`🔍 PAYMENT GET [${requestId}] - Keycloak ID validation: ${isValid ? '✅ Valid' : '❌ Invalid'} - ${keycloakId}`);
    } else {
      console.warn(`⚠️ PAYMENT GET [${requestId}] - No customer ID found in URL params!`);
    }
    
    // Redirect to the confirmed page with all parameters
    const origin = BASE_URL || request.nextUrl.origin;
    const redirectUrl = new URL('/checkout/confirmed', origin);
    
    // Set a status to indicate success
    redirectUrl.searchParams.append('status', 'success');
    
    // Add request ID for tracing
    redirectUrl.searchParams.append('request_id', requestId);
    
    // CRITICAL: Pass the Keycloak ID to the confirmation page
    if (keycloakId) {
      // Pass with multiple possible parameter names for compatibility
      redirectUrl.searchParams.append('keycloak_id', keycloakId);
      redirectUrl.searchParams.append('customer_id', keycloakId);
    }
    
    // Add all other parameters
    Object.entries(params).forEach(([key, value]) => {
      redirectUrl.searchParams.append(key, value);
    });
    
    console.log(`🔄 PAYMENT GET [${requestId}] - Redirecting to confirmation page: ${redirectUrl.toString()}`);
    return NextResponse.redirect(redirectUrl, { 
      headers: corsHeaders 
    });
  } catch (error) {
    console.error("❌ PAYMENT GET - Error:", error);
    
    // Even if there's an error, try to redirect to confirmed page with error status
    const origin = BASE_URL || new URL(request.url).origin;
    const errorRedirectUrl = new URL('/checkout/confirmed', origin);
    errorRedirectUrl.searchParams.append('status', 'error');
    errorRedirectUrl.searchParams.append('error_message', 'Failed to process payment confirmation. Please contact support.');
    errorRedirectUrl.searchParams.append('error_detail', String(error));
    
    console.log(`❌ PAYMENT GET - Error redirecting to: ${errorRedirectUrl.toString()}`);
    
    return NextResponse.redirect(errorRedirectUrl, { 
      headers: corsHeaders 
    });
  }
}