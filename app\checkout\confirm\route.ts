import { NextRequest, NextResponse } from "next/server";
import { safeFetch, safePost } from "@/lib/fetch-utils";

// Get the base URL from environment, with fallback
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

// Enhanced POST request handling - forwards the complete request
export async function POST(request: NextRequest) {
  try {
    // Use BASE_URL if available, otherwise fall back to request.nextUrl.origin
    const origin = BASE_URL || request.nextUrl.origin;
    const apiUrl = new URL('/api/checkout/confirm', origin);
    
    // Copy all search parameters to the new URL
    const url = new URL(request.url);
    url.searchParams.forEach((value, key) => {
      apiUrl.searchParams.append(key, value);
    });
    
    // Log the request URL and params for debugging
    console.log(`🔄 PAYMENT REDIRECT - Forwarding to API URL: ${apiUrl.toString()}`);
    
    // Try different approaches to get the body data
    let formData;
    let jsonData;
    let bodyText;
    
    try {
      // Try to get form data first
      formData = await request.formData();
      console.log(`✅ PAYMENT REDIRECT - Successfully parsed form data`);
      
      // Use the same URL but with POST and form data
      const response = await safePost(apiUrl.toString(), formData);
      
      if (response.ok) {
        // Redirect to confirmed page
        const redirectUrl = new URL('/checkout/confirmed', origin);
        
        // Add status parameter if not present
        if (!redirectUrl.searchParams.has('status')) {
          redirectUrl.searchParams.append('status', 'success');
        }
        
        // Copy form data to query params for GET request
        // Use Array.from to avoid linter errors with FormData iteration
        Array.from(formData.entries()).forEach(([key, value]) => {
          if (typeof value === 'string') {
            redirectUrl.searchParams.append(key, value);
          }
        });
        
        // Copy URL params that might not be in form data
        url.searchParams.forEach((value, key) => {
          if (!redirectUrl.searchParams.has(key)) {
            redirectUrl.searchParams.append(key, value);
          }
        });
        
        console.log(`🔄 PAYMENT REDIRECT - Redirecting to ${redirectUrl.toString()}`);
        return NextResponse.redirect(redirectUrl, { status: 303 });
      } else {
        throw new Error(`API returned status ${response.status}`);
      }
    } catch (formError) {
      console.log(`⚠️ PAYMENT REDIRECT - Form data approach failed: ${formError}`);
      
      try {
        // Try to get JSON data
        const clonedRequest = request.clone();
        bodyText = await clonedRequest.text();
        
        if (bodyText && bodyText.trim()) {
          try {
            jsonData = JSON.parse(bodyText);
            console.log(`✅ PAYMENT REDIRECT - Successfully parsed JSON data`);
            
            // Use the same URL but with POST and JSON data
            const response = await safePost(apiUrl.toString(), jsonData);
            
            if (response.ok) {
              // Handle successful response
              const redirectUrl = new URL('/checkout/confirmed', origin);
              
              // Add data from JSON
              Object.entries(jsonData).forEach(([key, value]) => {
                if (typeof value === 'string') {
                  redirectUrl.searchParams.append(key, value);
                }
              });
              
              // Add URL params
              url.searchParams.forEach((value, key) => {
                if (!redirectUrl.searchParams.has(key)) {
                  redirectUrl.searchParams.append(key, value);
                }
              });
              
              console.log(`🔄 PAYMENT REDIRECT - Redirecting to ${redirectUrl.toString()}`);
              return NextResponse.redirect(redirectUrl, { status: 303 });
            } else {
              throw new Error(`API returned status ${response.status}`);
            }
          } catch (jsonError) {
            console.log(`⚠️ PAYMENT REDIRECT - JSON parsing failed, using raw text: ${jsonError}`);
            
            // If we can't parse as JSON, send as raw text
            const response = await safePost(apiUrl.toString(), bodyText, {
              headers: { 'Content-Type': 'text/plain' }
            });
            
            if (response.ok) {
              // Handle successful response
              const redirectUrl = new URL('/checkout/confirmed', origin);
              
              // Just add URL params since we couldn't parse the body
              url.searchParams.forEach((value, key) => {
                redirectUrl.searchParams.append(key, value);
              });
              
              // Add status
              redirectUrl.searchParams.append('status', 'success');
              
              console.log(`🔄 PAYMENT REDIRECT - Redirecting to ${redirectUrl.toString()}`);
              return NextResponse.redirect(redirectUrl, { status: 303 });
            } else {
              throw new Error(`API returned status ${response.status}`);
            }
          }
        } else {
          throw new Error("Request body is empty");
        }
      } catch (textError) {
        console.log(`⚠️ PAYMENT REDIRECT - Text approach failed: ${textError}`);
        
        // Last resort: just forward the URL parameters
        console.log(`⚠️ PAYMENT REDIRECT - Trying URL parameters only approach`);
        
        // Build a query string from the URL parameters
        const params = new URLSearchParams();
        url.searchParams.forEach((value, key) => {
          params.append(key, value);
        });
        
        // Use the same URL but with POST and URL parameters
        const response = await safePost(apiUrl.toString(), params);
        
        if (response.ok) {
          // Handle successful response
          const redirectUrl = new URL('/checkout/confirmed', origin);
          
          // Add URL params
          url.searchParams.forEach((value, key) => {
            redirectUrl.searchParams.append(key, value);
          });
          
          // Add status
          redirectUrl.searchParams.append('status', 'success');
          
          console.log(`🔄 PAYMENT REDIRECT - Redirecting to ${redirectUrl.toString()}`);
          return NextResponse.redirect(redirectUrl, { status: 303 });
        } else {
          throw new Error(`API returned status ${response.status}`);
        }
      }
    }
  } catch (error) {
    console.error("❌ PAYMENT REDIRECT - Error forwarding POST request:", error);
    
    // Provide a fallback redirect to the error page
    const origin = BASE_URL || new URL(request.url).origin;
    const errorRedirectUrl = new URL('/checkout/confirmed', origin);
    errorRedirectUrl.searchParams.append('status', 'error');
    errorRedirectUrl.searchParams.append('error_message', 'Failed to process payment request. Please try again.');
    
    // Add any parameters from the request URL for debugging
    const url = new URL(request.url);
    url.searchParams.forEach((value, key) => {
      if (!errorRedirectUrl.searchParams.has(key)) {
        errorRedirectUrl.searchParams.append(key, value);
      }
    });
    
    console.log(`❌ PAYMENT REDIRECT - Error redirect to ${errorRedirectUrl.toString()}`);
    return NextResponse.redirect(errorRedirectUrl, { status: 303 });
  }
}

// Enhanced GET request handling with proper error handling
export async function GET(request: NextRequest) {
  try {
    // Use BASE_URL if available, otherwise fall back to request.nextUrl.origin
    const origin = BASE_URL || request.nextUrl.origin;
    const apiUrl = new URL('/api/checkout/confirm', origin);
    
    // Copy all search parameters to the new URL
    const url = new URL(request.url);
    url.searchParams.forEach((value, key) => {
      apiUrl.searchParams.append(key, value);
    });
    
    // Log the request URL for debugging
    console.log(`🔄 PAYMENT REDIRECT - Forwarding GET to API URL: ${apiUrl.toString()}`);
    
    try {
      // Make the API request
      const response = await safeFetch(apiUrl.toString(), {
        method: 'GET',
        headers: request.headers
      });
      
      // If the API redirects us, follow that redirect
      if (response.redirected && response.url) {
        console.log(`🔄 PAYMENT REDIRECT - API redirected to: ${response.url}`);
        return NextResponse.redirect(response.url, { status: 303 });
      }
      
      // Create default redirect to confirmed page
      const redirectUrl = new URL('/checkout/confirmed', origin);
      
      // Set default status
      redirectUrl.searchParams.append('status', 'success');
      
      // Copy all URL parameters
      url.searchParams.forEach((value, key) => {
        redirectUrl.searchParams.append(key, value);
      });
      
      // If we got a JSON response, also include any data from it
      try {
        const responseData = await response.json();
        if (responseData && typeof responseData === 'object') {
          Object.entries(responseData).forEach(([key, value]) => {
            if (typeof value === 'string') {
              redirectUrl.searchParams.append(key, value);
            }
          });
        }
      } catch {
        // Ignore JSON parsing errors, we already have URL parameters
      }
      
      console.log(`🔄 PAYMENT REDIRECT - GET redirecting to ${redirectUrl.toString()}`);
      return NextResponse.redirect(redirectUrl, { status: 303 });
    } catch (apiError) {
      console.error(`❌ PAYMENT REDIRECT - API request failed: ${apiError}`);
      throw apiError; // Re-throw to use the common error handler
    }
  } catch (error) {
    console.error("❌ PAYMENT REDIRECT - Error forwarding GET request:", error);
    
    // Provide a fallback redirect to the error page
    const origin = BASE_URL || new URL(request.url).origin;
    const errorRedirectUrl = new URL('/checkout/confirmed', origin);
    errorRedirectUrl.searchParams.append('status', 'error');
    errorRedirectUrl.searchParams.append('error_message', 'Failed to process payment request. Please try again.');
    
    // Add any parameters from the request URL for debugging
    const url = new URL(request.url);
    url.searchParams.forEach((value, key) => {
      if (!errorRedirectUrl.searchParams.has(key)) {
        errorRedirectUrl.searchParams.append(key, value);
      }
    });
    
    console.log(`❌ PAYMENT REDIRECT - GET error redirect to ${errorRedirectUrl.toString()}`);
    return NextResponse.redirect(errorRedirectUrl, { status: 303 });
  }
}
