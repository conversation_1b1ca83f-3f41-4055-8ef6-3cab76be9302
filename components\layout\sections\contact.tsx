"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import {
  Building2,
  Clock,
  Mail,
  Phone,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
// Select imports removed as we're using Input for subject
import { Textarea } from "@/components/ui/textarea";
import { useState, useEffect, useRef } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Script from "next/script";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

const formSchema = z.object({
  firstName: z.string().min(2).max(255),
  lastName: z.string().min(2).max(255),
  email: z.string().email(),
  phone: z.string().min(10).max(15),
  message: z.string().min(5).max(32000).refine(
    (val) => val.trim().length >= 5,
    {
      message: "Message must be at least 5 characters long",
    }
  ),
  recaptchaToken: z.string().optional(),
});

type ContactSectionProps = {
  sectionComponent?: string;
  sectionId?: number;
};

export const ContactSection = ({ sectionComponent, sectionId }: ContactSectionProps = {}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const recaptchaRef = useRef<HTMLDivElement>(null);
  const [recaptchaLoaded, setRecaptchaLoaded] = useState(false);
  const [serverStatus, setServerStatus] = useState<'checking' | 'online' | 'offline'>('checking');

  // reCAPTCHA site key (public) - should come from environment variable
  // This is the only reCAPTCHA key that should be used on the client side
  // Using Google's test key for reCAPTCHA v2
  const RECAPTCHA_SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'; // Test key

  // Get the MailForm API URL from environment variables
  const MAILFORM_API_URL = process.env.NEXT_PUBLIC_MAILFORM_API_URL || 'http://localhost:3003/onebiz';

  // Initialize reCAPTCHA when component mounts
  useEffect(() => {
    // Set recaptchaLoaded to true by default to prevent button from being stuck in loading state
    setRecaptchaLoaded(true);

    // Check server status
    checkServerStatus();

    // Check server status every 30 seconds
    const intervalId = setInterval(checkServerStatus, 30000);

    return () => {
      clearInterval(intervalId);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to check if the MailForm server is online
  const checkServerStatus = () => {
    // Try to get the health endpoint by replacing the target with 'health'
    const healthUrl = MAILFORM_API_URL.replace(/\/[^\/]*$/, '/health');
    console.log('Checking server status at:', healthUrl);

    // Create a new XMLHttpRequest object
    const xhr = new XMLHttpRequest();

    // Configure it: GET-request for the URL
    xhr.open('GET', healthUrl);
    xhr.timeout = 5000; // 5 seconds timeout

    // Send the request
    xhr.send();

    // This will be called after the response is received
    xhr.onload = function() {
      if (xhr.status === 200) {
        setServerStatus('online');
        console.log('Server health:', xhr.responseText);
        try {
          const healthData = JSON.parse(xhr.responseText);
          console.log('Server health data:', healthData);
        } catch (e) {
          console.error('Error parsing health response:', e);
        }
      } else {
        setServerStatus('offline');
        console.error('Server returned status:', xhr.status);
      }
    };

    xhr.onerror = function() {
      setServerStatus('offline');
      console.error('Server status check failed');
    };

    xhr.ontimeout = function() {
      setServerStatus('offline');
      console.error('Server status check timed out');
    };
  };

  const form = useForm<
    z.infer<typeof formSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      message: "",
      recaptchaToken: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);
      setFormStatus('idle');
      setErrorMessage(null);

      // Get the reCAPTCHA response from the checkbox
      let recaptchaResponse = '';
      if (typeof window !== 'undefined' && window.grecaptcha) {
        recaptchaResponse = window.grecaptcha.getResponse();

        if (!recaptchaResponse) {
          setFormStatus('error');
          setErrorMessage('Please complete the reCAPTCHA verification.');
          setIsSubmitting(false);
          return;
        }
      }

      console.log('Submitting form:', values);

      // Create FormData object for submission
      const formData = new FormData();
      // Use 'from' instead of 'email' as expected by MailForm
      formData.append('from', values.email);
      // Combine firstName and lastName into 'name' field
      formData.append('name', `${values.firstName} ${values.lastName}`);
      // Send phone number as subject to match the template
      formData.append('subject', values.phone);
      // Use 'body' instead of 'message' as expected by MailForm
      formData.append('body', values.message);
      // Add product field
      formData.append('product', 'Website Contact Form');
      // Also send phone as a separate field in case the template is updated
      formData.append('phone', values.phone);

      // Add reCAPTCHA response if available
      if (recaptchaResponse) {
        formData.append('g-recaptcha-response', recaptchaResponse);
      }

      // Log form data being sent
      console.log('Form data being sent:');
      for (const pair of formData.entries()) {
        if (pair[0] === 'g-recaptcha-response') {
          console.log(pair[0] + ': ' + pair[1].toString().substring(0, 20) + '...');
        } else {
          console.log(pair[0] + ': ' + pair[1]);
        }
      }

      // Check if server is offline
      if (serverStatus === 'offline') {
        setFormStatus('error');
        setErrorMessage('The server appears to be offline. Please try again later.');
        setIsSubmitting(false);
        return;
      }

      console.log('Sending to MailForm API URL:', MAILFORM_API_URL);

      // Create XMLHttpRequest to send the form data directly to MailForm
      const xhr = new XMLHttpRequest();
      xhr.open('POST', MAILFORM_API_URL);
      xhr.timeout = 10000; // 10 seconds timeout

      // Define a type for the form submission result
      interface FormSubmissionResult {
        success: boolean;
        message: string;
      }

      // Create a promise to handle the XMLHttpRequest
      const result = await new Promise<FormSubmissionResult>((resolve) => {
        // Handle successful response
        xhr.onload = function() {
          console.log('Response status:', xhr.status);

          // For any 2xx status code, consider it a success
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve({ success: true, message: 'Message sent successfully!' });
            return;
          }

          // For redirects (301, 302), also consider it a success
          if (xhr.status === 301 || xhr.status === 302) {
            resolve({ success: true, message: 'Message sent successfully! (Server redirected)' });
            return;
          }

          // For other status codes, try to parse the response
          try {
            const text = xhr.responseText;
            console.log('Raw response:', text);

            // If there's content, try to parse it as JSON
            if (text && text.trim() !== '') {
              try {
                const data = JSON.parse(text);
                console.log('Server response:', data);

                if (data.error || data.message === "captcha verification failed") {
                  // Handle specific captcha error
                  if (data.message === "captcha verification failed") {
                    resolve({
                      success: false,
                      message: 'CAPTCHA verification failed. Please try again.'
                    });
                  }
                  // Handle validation errors
                  else if (data.error === "validation_error" && data.problems) {
                    // Define a type for validation problems
                    interface ValidationProblem {
                      field: string;
                      violated: string;
                      expect?: {
                        minimum?: number;
                        maximum?: number;
                      };
                    }

                    // Format validation errors in a user-friendly way
                    const errorMessages = data.problems.map((problem: ValidationProblem) => {
                      if (problem.field === "body" && problem.violated === "length") {
                        return `Message must be between ${problem.expect?.minimum || 5} and ${problem.expect?.maximum || 32000} characters`;
                      }
                      return `${problem.field}: ${problem.violated} validation failed`;
                    });

                    resolve({
                      success: false,
                      message: `Validation error: ${errorMessages.join(", ")}`
                    });
                  }
                  else {
                    resolve({
                      success: false,
                      message: `Submission error: ${JSON.stringify(data.problems || data.error || data.message)}`
                    });
                  }
                } else {
                  // If we got here with a non-200 status but parseable JSON, show the error
                  resolve({
                    success: false,
                    message: `Server returned status ${xhr.status}: ${JSON.stringify(data)}`
                  });
                }
              } catch (e) {
                console.error('JSON parse error:', e);
                // If we can't parse the response, show the raw text
                resolve({
                  success: false,
                  message: `Server returned status ${xhr.status}: ${text}`
                });
              }
            } else {
              // Empty response with error status
              resolve({
                success: false,
                message: `Server returned status ${xhr.status} with no content`
              });
            }
          } catch (e) {
            // If we can't even get the response text
            console.error('Error reading response:', e);
            resolve({
              success: false,
              message: `Server returned status ${xhr.status} but response could not be read`
            });
          }
        };

        // Handle network errors
        xhr.onerror = function() {
          console.error('Network error occurred');
          resolve({
            success: false,
            message: 'A network error occurred. Please check your connection and try again.'
          });
        };

        // Handle timeouts
        xhr.ontimeout = function() {
          console.error('Request timed out');
          resolve({
            success: false,
            message: 'The request timed out. Please try again later.'
          });
        };

        // Send the form data
        xhr.send(formData);
      });

      if (result.success) {
        setFormStatus('success');
        form.reset();

        // Reset reCAPTCHA
        if (typeof window !== 'undefined' && window.grecaptcha) {
          window.grecaptcha.reset();
        }
      } else {
        setFormStatus('error');
        setErrorMessage(result.message || 'Failed to send message. Please try again later.');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setFormStatus('error');
      setErrorMessage('An unexpected error occurred. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <SectionContainer
      id="contact"
      sectionComponent={sectionComponent}
      sectionId={sectionId}
    >
      {/* reCAPTCHA Script for v2 */}
      <Script
        src="https://www.google.com/recaptcha/api.js"
        strategy="lazyOnload"
      />
      <SectionHeader
        subTitle="Contact"
        title="Connect With Us"
        description=" Stay in touch with us for updates, support, and valuable insights.
          We’re here to help you every step of the way!"
      />
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div className="flex flex-col gap-6 *:rounded-lg *:p-6 *:border">
            <div>
              <div className="flex items-center gap-3 mb-3">
                <Building2 className="size-5" />
                <div className="font-bold">
                  Location:
                </div>
              </div>
              <div className="text-muted-foreground">
                1905, Cyber One, Plot No. 4 & 6,
                Sector 30A, Vashi, Navi Mumbai -
                400 703.
              </div>
            </div>

            <div>
              <div className="flex items-center gap-3 mb-3">
                <Phone className="size-5" />
                <div className="font-bold">
                  Call us:
                </div>
              </div>
              <div className="text-muted-foreground">
                (+91) ************
              </div>
            </div>

            <div>
              <div className="flex items-center gap-3 mb-3">
                <Mail className="size-5" />
                <div className="font-bold">
                  Email us:
                </div>
              </div>
              <div className="text-muted-foreground">
                <EMAIL>
              </div>
            </div>

            <div>
              <div className="flex items-center gap-3 mb-3">
                <Clock className="size-5" />
                <div className="font-bold">
                  Business Hours:
                </div>
              </div>
              <div className="text-muted-foreground">
                Tuesday to Saturday, 9 AM - 5 PM
              </div>
            </div>
          </div>
        </div>

        <Card className="bg-muted">
          <CardContent className="pt-6">
            {/* Server Status Indicator */}
            {/* <div className="flex items-center gap-2 mb-4 p-2 rounded bg-gray-50">
              <div
                className={`w-3 h-3 rounded-full ${
                  serverStatus === 'online'
                    ? 'bg-green-500'
                    : serverStatus === 'offline'
                      ? 'bg-red-500'
                      : 'bg-yellow-500 animate-pulse'
                }`}
              />
              <span className="text-sm text-gray-600">
                {serverStatus === 'online'
                  ? 'Server Online'
                  : serverStatus === 'offline'
                    ? 'Server Offline - Form submissions may not work'
                    : 'Checking server status...'}
              </span>
            </div> */}

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(
                  onSubmit
                )}
                className="grid w-full gap-4"
              >
                <div className="flex flex-col md:!flex-row gap-6">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          First Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Leopoldo"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>
                          Last Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Miranda"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex flex-col gap-1.5">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Email
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex flex-col gap-1.5">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mobile Number
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="Enter your mobile number"
                            {...field}
                          />
                        </FormControl>
                        <p className="text-xs text-muted-foreground mt-1">
                          Please enter a valid mobile number (minimum 10 digits)
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex flex-col gap-1.5">
                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Message
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            rows={5}
                            placeholder="Your message... (minimum 5 characters)"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <p className="text-xs text-muted-foreground mt-1">
                          Please enter at least 5 characters
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {formStatus === 'success' && (
                  <Alert variant="success">
                    <CheckCircle className="h-4 w-4" />
                    <AlertTitle>Success!</AlertTitle>
                    <AlertDescription>
                      Your message has been sent successfully. We&apos;ll get back to you soon.
                    </AlertDescription>
                  </Alert>
                )}

                {formStatus === 'error' && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>
                      {errorMessage || 'Failed to send message. Please try again later.'}
                    </AlertDescription>
                  </Alert>
                )}

                {/* reCAPTCHA v2 Checkbox */}
                <div className="my-4">
                  <div
                    ref={recaptchaRef}
                    className="g-recaptcha"
                    data-sitekey={RECAPTCHA_SITE_KEY}
                  ></div>
                </div>

                {/* reCAPTCHA disclaimer */}
                <p className="text-xs text-muted-foreground mt-2">
                  This site is protected by reCAPTCHA and the Google{' '}
                  <a href="https://policies.google.com/privacy" target="_blank" rel="noopener noreferrer" className="underline">
                    Privacy Policy
                  </a>{' '}
                  and{' '}
                  <a href="https://policies.google.com/terms" target="_blank" rel="noopener noreferrer" className="underline">
                    Terms of Service
                  </a>{' '}
                  apply.
                </p>

                <Button
                  className="mt-4"
                  type="submit"
                  disabled={isSubmitting || !recaptchaLoaded}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : !recaptchaLoaded ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Send message'
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>

          <CardFooter>
            {formStatus === 'success' && (
              <p className="text-sm text-muted-foreground">
                Thank you for reaching out! We&apos;ll respond as soon as possible.
              </p>
            )}
          </CardFooter>
        </Card>
      </section>
    </SectionContainer>
  );
};
