/**
 * Email API - Invoice Endpoint
 *
 * Handles sending invoice payment confirmation emails.
 * Supports both GET and POST methods.
 */

import { NextRequest, NextResponse } from "next/server";
import { sendInvoicePaymentEmailServer, sendInvoicePaymentEmailWithRetry } from "@/src/services/emailService";
import { safeFetch } from "@/lib/fetch-utils";
import { getInvoiceById } from "@/src/services/lago/invoiceService";

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tracking-ID',
};

// External email API endpoint
const EMAIL_API_URL = 'https://onepay.cubeone.in/api/send-subscription-email';
const EMAIL_API_TOKEN = '1231934a-c657-48d3-882e-125e618eaf1b';

// Log levels for detailed logging
const LOG_LEVELS = {
  INFO: '📧 EMAIL API',
  SUCCESS: '✅ EMAIL API',
  WARNING: '⚠️ EMAIL API',
  ERROR: '❌ EMAIL API'
};

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

/**
 * GET handler for sending invoice payment emails
 *
 * Query parameters:
 * - invoice_id: The Lago invoice ID (required)
 * - retry: Whether to use retry mechanism (optional, default: true)
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const invoiceId = url.searchParams.get('invoice_id');
    const trackingId = request.headers.get('X-Tracking-ID') || `get_${Date.now()}`;
    const useRetry = url.searchParams.get('retry') !== 'false'; // Default to true

    // Validate required parameters
    if (!invoiceId) {
      console.error('❌ EMAIL API - Missing required parameter: invoice_id');
      return NextResponse.json(
        { success: false, message: 'Missing required parameter: invoice_id' },
        { status: 400, headers: corsHeaders }
      );
    }

    console.log(`📧 EMAIL API [${trackingId}] - Sending invoice payment email for invoice: ${invoiceId}`);

    // Verify that the invoice exists and is in the correct state
    try {
      const invoice = await getInvoiceById(invoiceId);
      if (!invoice) {
        console.error(`❌ EMAIL API [${trackingId}] - Invoice not found: ${invoiceId}`);
        return NextResponse.json(
          { success: false, message: 'Invoice not found', error: 'INVOICE_NOT_FOUND' },
          { status: 404, headers: corsHeaders }
        );
      }

      console.log(`📧 EMAIL API [${trackingId}] - Invoice details:`, {
        id: invoice.id,
        lago_id: invoice.lago_id,
        payment_status: invoice.payment_status,
        status: invoice.status
      });

      // Check if the invoice is in the correct state for sending an email
      if (invoice.payment_status !== 'succeeded') {
        console.warn(`⚠️ EMAIL API [${trackingId}] - Invoice payment status is not 'succeeded': ${invoice.payment_status}`);
        // Continue anyway, but log the warning
      }
    } catch (error) {
      console.error(`❌ EMAIL API [${trackingId}] - Error verifying invoice:`, error);
      // Continue anyway, as the external email API might have its own validation
    }

    // Log the invoice details for debugging
    console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Invoice details for email notification:`, {
      invoice_id: invoiceId,
      retry: useRetry
    });

    // Verify that the invoice ID is valid
    if (!invoiceId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - Invalid invoice ID format: ${invoiceId}`);
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid invoice ID format',
          error: 'INVALID_INVOICE_ID_FORMAT',
          invoice_id: invoiceId
        },
        { status: 400, headers: corsHeaders }
      );
    }

    // Check if this is a duplicate request
    const emailKey = `email_api_${invoiceId}`;

    // Initialize the global set if it doesn't exist
    if (!global.processedEmailRequests) {
      global.processedEmailRequests = new Set<string>();
    }

    // Check if we've already processed this email in this session
    const alreadyProcessed = global.processedEmailRequests.has(emailKey);

    if (alreadyProcessed) {
      console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Email for invoice ${invoiceId} already processed, returning cached success`);
      return NextResponse.json(
        {
          success: true,
          message: 'Email already sent (API deduplicated)',
          invoice_id: invoiceId,
          lago_invoice_id: invoiceId,
          deduplicated: true
        },
        { status: 200, headers: corsHeaders }
      );
    }

    // Mark this email as processed to prevent duplicate processing
    global.processedEmailRequests.add(emailKey);

    // Send the email with or without retry mechanism
    console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Sending email with lago_invoice_id: ${invoiceId}`);
    const emailResult = useRetry
      ? await sendInvoicePaymentEmailWithRetry(invoiceId, 3, trackingId)
      : await sendInvoicePaymentEmailServer(invoiceId, trackingId);

    if (emailResult.success) {
      // Check if this was a deduplicated email
      if (emailResult.message === 'Email already sent (deduplicated)') {
        console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Email already sent for invoice: ${invoiceId}, skipping duplicate`);
        return NextResponse.json(
          {
            success: true,
            message: 'Email already sent (deduplicated)',
            invoice_id: invoiceId,
            lago_invoice_id: invoiceId,
            deduplicated: true
          },
          { status: 200, headers: corsHeaders }
        );
      }

      console.log(`${LOG_LEVELS.SUCCESS} [${trackingId}] - Successfully sent invoice payment email: ${invoiceId}`);
      return NextResponse.json(
        {
          success: true,
          message: 'Subscription email sent successfully',
          invoice_id: invoiceId,
          lago_invoice_id: invoiceId // Include the lago_invoice_id in the response
        },
        { status: 200, headers: corsHeaders }
      );
    } else {
      console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - Failed to send email:`, emailResult.error);
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to send subscription email',
          error: emailResult.error,
          invoice_id: invoiceId,
          lago_invoice_id: invoiceId // Include the lago_invoice_id in the response
        },
        { status: 500, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('❌ EMAIL API - Exception in GET /api/email/invoice:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500, headers: corsHeaders }
    );
  }
}

/**
 * POST handler for sending invoice payment emails
 *
 * Request body:
 * - invoice_id: The Lago invoice ID (required)
 * - retry: Whether to use retry mechanism (optional, default: true)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const trackingId = request.headers.get('X-Tracking-ID') || `post_${Date.now()}`;
    const useRetry = body.retry !== false; // Default to true

    // Validate required fields
    if (!body.invoice_id) {
      console.error('❌ EMAIL API - Missing required field: invoice_id');
      return NextResponse.json(
        { success: false, message: 'Missing required field: invoice_id' },
        { status: 400, headers: corsHeaders }
      );
    }

    console.log(`📧 EMAIL API [${trackingId}] - Sending invoice payment email for invoice: ${body.invoice_id}`);
    console.log(`📧 EMAIL API [${trackingId}] - Request body:`, {
      invoice_id: body.invoice_id,
      retry: useRetry
    });

    // Verify that the invoice exists and is in the correct state
    try {
      const invoice = await getInvoiceById(body.invoice_id);
      if (!invoice) {
        console.error(`❌ EMAIL API [${trackingId}] - Invoice not found: ${body.invoice_id}`);
        return NextResponse.json(
          { success: false, message: 'Invoice not found', error: 'INVOICE_NOT_FOUND' },
          { status: 404, headers: corsHeaders }
        );
      }

      console.log(`📧 EMAIL API [${trackingId}] - Invoice details:`, {
        id: invoice.id,
        lago_id: invoice.lago_id,
        payment_status: invoice.payment_status,
        status: invoice.status
      });

      // Check if the invoice is in the correct state for sending an email
      if (invoice.payment_status !== 'succeeded') {
        console.warn(`⚠️ EMAIL API [${trackingId}] - Invoice payment status is not 'succeeded': ${invoice.payment_status}`);
        // Continue anyway, but log the warning
      }
    } catch (error) {
      console.error(`❌ EMAIL API [${trackingId}] - Error verifying invoice:`, error);
      // Continue anyway, as the external email API might have its own validation
    }

    // Log the invoice details for debugging
    console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Invoice details for email notification:`, {
      invoice_id: body.invoice_id,
      retry: useRetry
    });

    // Verify that the invoice ID is valid
    if (!body.invoice_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - Invalid invoice ID format: ${body.invoice_id}`);
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid invoice ID format',
          error: 'INVALID_INVOICE_ID_FORMAT',
          invoice_id: body.invoice_id
        },
        { status: 400, headers: corsHeaders }
      );
    }

    // Check if this is a duplicate request
    const emailKey = `email_api_${body.invoice_id}`;

    // Initialize the global set if it doesn't exist
    if (!global.processedEmailRequests) {
      global.processedEmailRequests = new Set<string>();
    }

    // Check if we've already processed this email in this session
    const alreadyProcessed = global.processedEmailRequests.has(emailKey);

    if (alreadyProcessed) {
      console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Email for invoice ${body.invoice_id} already processed, returning cached success`);
      return NextResponse.json(
        {
          success: true,
          message: 'Email already sent (API deduplicated)',
          invoice_id: body.invoice_id,
          lago_invoice_id: body.invoice_id,
          deduplicated: true
        },
        { status: 200, headers: corsHeaders }
      );
    }

    // Mark this email as processed to prevent duplicate processing
    global.processedEmailRequests.add(emailKey);

    // Send the email with or without retry mechanism
    console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Sending email with lago_invoice_id: ${body.invoice_id}`);
    const emailResult = useRetry
      ? await sendInvoicePaymentEmailWithRetry(body.invoice_id, 3, trackingId)
      : await sendInvoicePaymentEmailServer(body.invoice_id, trackingId);

    if (emailResult.success) {
      // Check if this was a deduplicated email
      if (emailResult.message === 'Email already sent (deduplicated)') {
        console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Email already sent for invoice: ${body.invoice_id}, skipping duplicate`);
        return NextResponse.json(
          {
            success: true,
            message: 'Email already sent (deduplicated)',
            invoice_id: body.invoice_id,
            lago_invoice_id: body.invoice_id,
            deduplicated: true
          },
          { status: 200, headers: corsHeaders }
        );
      }

      console.log(`${LOG_LEVELS.SUCCESS} [${trackingId}] - Successfully sent invoice payment email: ${body.invoice_id}`);
      return NextResponse.json(
        {
          success: true,
          message: 'Subscription email sent successfully',
          invoice_id: body.invoice_id,
          lago_invoice_id: body.invoice_id // Include the lago_invoice_id in the response
        },
        { status: 200, headers: corsHeaders }
      );
    } else {
      console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - Failed to send email:`, emailResult.error);
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to send subscription email',
          error: emailResult.error,
          invoice_id: body.invoice_id,
          lago_invoice_id: body.invoice_id // Include the lago_invoice_id in the response
        },
        { status: 500, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('❌ EMAIL API - Exception in POST /api/email/invoice:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
