"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  extractProductCode,
  extractPlanType,
  extractPlanDuration,
  isTrialPlan,
  getProductDisplayName,
  getPlanTypeDisplayName,
  getPlanDurationDisplayName,
  generatePlanCode,
  formatPlanDisplayName,
} from "@/utils/plan-utils";

export default function PlanDisplayPage() {
  const [customPlanCode, setCustomPlanCode] = useState("onerestro_premium_m");
  
  // Example plan codes to demonstrate
  const examplePlanCodes = [
    "onerestro_starter_m",
    "onerestro_premium_y",
    "onebiz_trial",
    "one-hr_enterprise_m",
    "one-biz-premium-yearly",
    "onebiz_standard",
    "onehr_basic_quarterly",
    "custom_app_starter_m",
    "project123_enterprise_m",
  ];
  
  // Function to analyze a plan code
  const analyzePlanCode = (planCode: string) => {
    const productCode = extractProductCode(planCode);
    const planType = extractPlanType(planCode);
    const planDuration = extractPlanDuration(planCode);
    const trial = isTrialPlan(planCode);
    
    return {
      planCode,
      productCode,
      productName: getProductDisplayName(productCode),
      planType,
      planTypeName: planType ? getPlanTypeDisplayName(planType) : null,
      planDuration,
      planDurationName: planDuration ? getPlanDurationDisplayName(planDuration) : null,
      isTrial: trial,
      displayName: formatPlanDisplayName(planCode),
    };
  };
  
  // State for plan code builder
  const [productName, setProductName] = useState("onerestro");
  const [planType, setPlanType] = useState("starter");
  const [planDuration, setPlanDuration] = useState("monthly");
  const [isTrial, setIsTrial] = useState(false);
  const [generatedPlanCode, setGeneratedPlanCode] = useState("");
  
  // Generate a plan code from components
  const handleGeneratePlanCode = () => {
    const code = generatePlanCode(
      productName,
      isTrial ? undefined : planType,
      isTrial ? undefined : planDuration,
      isTrial
    );
    setGeneratedPlanCode(code);
    setCustomPlanCode(code);
  };
  
  // Custom plan code analysis
  const customCodeAnalysis = analyzePlanCode(customPlanCode);
  
  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">Plan Code Display</h1>
      <p className="text-muted-foreground mb-8">
        Visualize how plan codes are parsed and displayed to users.
      </p>
      
      <div className="grid gap-6 md:grid-cols-2 mb-10">
        <Card>
          <CardHeader>
            <CardTitle>Custom Plan Code</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="planCode">Plan Code</Label>
                  <Input
                    id="planCode"
                    value={customPlanCode}
                    onChange={(e) => setCustomPlanCode(e.target.value)}
                    placeholder="e.g., onerestro_premium_m"
                    className="mt-1"
                  />
                </div>
                <div className="pt-7">
                  <Button 
                    variant="outline"
                    onClick={() => setCustomPlanCode("")}
                    size="sm"
                  >
                    Clear
                  </Button>
                </div>
              </div>
              
              <div className="p-4 bg-muted rounded-lg">
                <h3 className="font-medium mb-2">Analysis</h3>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <div className="font-medium">Product Code:</div>
                  <div>{customCodeAnalysis.productCode}</div>
                  
                  <div className="font-medium">Product Name:</div>
                  <div>{customCodeAnalysis.productName}</div>
                  
                  <div className="font-medium">Plan Type:</div>
                  <div>{customCodeAnalysis.planType || "—"}</div>
                  
                  <div className="font-medium">Plan Type Display:</div>
                  <div>{customCodeAnalysis.planTypeName || "—"}</div>
                  
                  <div className="font-medium">Duration:</div>
                  <div>{customCodeAnalysis.planDuration || "—"}</div>
                  
                  <div className="font-medium">Duration Display:</div>
                  <div>{customCodeAnalysis.planDurationName || "—"}</div>
                  
                  <div className="font-medium">Is Trial:</div>
                  <div>{customCodeAnalysis.isTrial ? "Yes" : "No"}</div>
                  
                  <div className="font-medium">Formatted Display:</div>
                  <div className="font-semibold">{customCodeAnalysis.displayName}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Plan Code Builder</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="productName">Product Name</Label>
                <Input
                  id="productName"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  placeholder="e.g., onerestro"
                />
              </div>
              
              <div className="flex gap-4">
                <div className="space-y-2 flex-1">
                  <Label htmlFor="planType">Plan Type</Label>
                  <Input
                    id="planType"
                    value={planType}
                    onChange={(e) => setPlanType(e.target.value)}
                    placeholder="e.g., starter"
                    disabled={isTrial}
                  />
                </div>
                
                <div className="space-y-2 flex-1">
                  <Label htmlFor="planDuration">Plan Duration</Label>
                  <Input
                    id="planDuration"
                    value={planDuration}
                    onChange={(e) => setPlanDuration(e.target.value)}
                    placeholder="e.g., monthly"
                    disabled={isTrial}
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isTrial"
                  checked={isTrial}
                  onChange={(e) => setIsTrial(e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <Label htmlFor="isTrial">Trial Plan</Label>
              </div>
              
              <Button onClick={handleGeneratePlanCode} className="w-full">
                Generate Plan Code
              </Button>
              
              {generatedPlanCode && (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <div className="text-sm font-medium mb-1">Generated Code:</div>
                  <div className="font-mono p-2 bg-background border rounded">
                    {generatedPlanCode}
                  </div>
                  <div className="text-sm font-medium mt-3 mb-1">Display Name:</div>
                  <div className="font-semibold">
                    {formatPlanDisplayName(generatedPlanCode)}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card className="mb-10">
        <CardHeader>
          <CardTitle>Example Plan Codes</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plan Code</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Plan Type</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Trial</TableHead>
                <TableHead>Display Name</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {examplePlanCodes.map((code) => {
                const analysis = analyzePlanCode(code);
                return (
                  <TableRow key={code}>
                    <TableCell className="font-mono">{code}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{analysis.productName}</Badge>
                    </TableCell>
                    <TableCell>
                      {analysis.planTypeName ? (
                        <Badge 
                          variant={
                            analysis.planType === "premium" ? "default" : 
                            analysis.planType === "enterprise" ? "destructive" : 
                            "secondary"
                          }
                        >
                          {analysis.planTypeName}
                        </Badge>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell>
                      {analysis.planDurationName ? (
                        <Badge variant="outline">
                          {analysis.planDurationName}
                        </Badge>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell>
                      {analysis.isTrial ? (
                        <Badge variant="warning">Trial</Badge>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell className="font-medium">
                      {analysis.displayName}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 