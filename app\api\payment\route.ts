import { NextRequest, NextResponse } from "next/server";
import { sanitizeKeycloakId, isValidKeycloakId } from "@/src/services/keycloakService";
import { safeFetch } from "@/lib/fetch-utils";

// Get the base URL from environment, with fallback
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.email || !body.fullName || !body.price || !body.items?.length) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // CRITICAL: Extract the Keycloak ID for payment
    // Try various possible field names - prioritize explicit keycloakId field
    const keycloakId = body.keycloakId || body.customerId || null;

    if (!keycloakId) {
      console.error("❌ PAYMENT API - No Keycloak ID provided");
      return NextResponse.json(
        { error: "Customer ID (Keycloak ID) is required" },
        { status: 400 }
      );
    }

    // Sanitize the Keycloak ID if possible
    const sanitizedId = sanitizeKeycloakId(keycloakId);
    const idToUse = sanitizedId || keycloakId;

    // Log validation information
    const isValidId = isValidKeycloakId(idToUse);
    console.log(`🔍 PAYMENT API - Keycloak ID validation: ${isValidId ? '✅ Valid' : '❌ Invalid'} - ${idToUse}`);

    // Extract the necessary data for payment processing
    const {
      fullName,
      email,
      phone,
      address1,
      city,
      product,
      price,
      gstNumber,
      businessName,
    } = body;

    // Determine the origin to use for redirect URL
    // Use the environment variable first, then fall back to request.nextUrl.origin
    const origin = BASE_URL || request.nextUrl.origin;

    // Construct the payment provider request
    const paymentRequestBody = {
      name: fullName,
      email: email,
      mobile: phone,
      product_name: product,
      amount: price,  // This should be in the smallest currency unit
      currency: "INR",
      description: "Purchase from OneBiz",
      customer_id: idToUse,  // CRITICAL: Use the sanitized Keycloak ID as customer_id
      address: `${address1}, ${city}`,
      selected_gateway: "Razorpay",
      redirect_url: `${origin}/checkout/confirm`,
      gst_number: gstNumber || undefined,
      business_name: businessName || undefined,
    };

    // Log the customer ID being used in the payment request
    console.log(`📤 PAYMENT API - Using customer_id in payment request: ${idToUse}`);
    console.log(`🔄 PAYMENT API - Initiating payment for ${email}`);
    console.log(`🔗 PAYMENT API - Using redirect URL: ${paymentRequestBody.redirect_url}`);

    // Make the actual request to your payment provider
    const response = await safeFetch("https://onepay.cubeone.in/api/checkout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
      body: JSON.stringify(paymentRequestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ PAYMENT API - Payment initiation failed:", response.status, errorText);
      return NextResponse.json(
        { error: `Payment initiation failed: ${response.statusText}` },
        { status: response.status }
      );
    }

    // Process the response from the payment provider
    const data = await response.json();
    console.log("✅ PAYMENT API - Payment initiation response received");

    if (data && data.data) {
      // Return the payment URL to the client along with the Keycloak ID
      return NextResponse.json({
        url: data.data,
        customerId: idToUse,
        keycloak_id: idToUse // Also include with underscore format for consistency
      });
    } else {
      return NextResponse.json(
        { error: "Invalid response from payment provider" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("❌ PAYMENT API - Payment processing error:", error);
    return NextResponse.json(
      { error: "An error occurred while processing the payment." },
      { status: 500 }
    );
  }
}
