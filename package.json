{"name": "onebiz", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.1", "@bschauer/strapi-mcp-server": "^2.4.0", "@devnomic/marquee": "^1.0.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/nextjs": "^9.9.0", "@tabler/icons-react": "^3.30.0", "@tanstack/react-query": "^5.74.4", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.3.0", "framer-motion": "^7.10.3", "graphql": "^16.10.0", "keycloak-js": "^26.2.0", "lottie-react": "^2.4.1", "lucide-react": "^0.453.0", "motion": "^12.4.7", "next": "^14.2.16", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.1", "sharp": "^0.33.5", "sonner": "^2.0.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "zod": "3.24.0-canary.20241016T212913"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}