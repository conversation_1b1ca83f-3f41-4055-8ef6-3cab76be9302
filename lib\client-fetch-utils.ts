"use client";

// Re-export the server functions for use in client components
import { safeFetch, safePost } from './fetch-utils';

// Export the utility functions
export { safeFetch, safePost };

/**
 * A simple utility for handling fetch requests with proper error handling
 * specifically for client components
 */
export async function clientFetch<T = any>(url: string, options: RequestInit = {}): Promise<T> {
  try {
    const response = await safeFetch(url, options);
    
    if (!response.ok) {
      // Try to parse error message if available
      try {
        const errorData = await response.json();
        throw new Error(errorData.message || `Request failed with status ${response.status}`);
      } catch (e) {
        // If JSON parsing fails, use status text
        throw new Error(`Request failed with status ${response.status}: ${response.statusText}`);
      }
    }
    
    // Check content type to determine how to parse response
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json() as T;
    } else {
      // Return the raw response for non-JSON responses
      return response as unknown as T;
    }
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

/**
 * A simple utility for making POST requests with proper error handling
 * specifically for client components
 */
export async function clientPost<T = any>(
  url: string, 
  body: any, 
  options: Omit<RequestInit, 'method' | 'body'> = {}
): Promise<T> {
  return clientFetch<T>(url, {
    method: 'POST',
    body: body instanceof FormData || body instanceof URLSearchParams || 
          body instanceof ReadableStream || typeof body === 'string' ? 
          body : JSON.stringify(body),
    headers: {
      ...(!options.headers && !(body instanceof FormData) && !(body instanceof URLSearchParams) && 
        !(body instanceof ReadableStream) && typeof body !== 'string' ? 
        { 'Content-Type': 'application/json' } : {}),
      ...(options.headers || {})
    },
    ...options
  });
} 