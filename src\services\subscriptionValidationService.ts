/**
 * Subscription Validation Service
 *
 * Centralizes all subscription validation logic to enforce business rules:
 * - Allow trial only if user has never used it for this product and has no active paid plan
 * - Block trial if user has already used it or has a paid plan
 * - Prevent subscribing to the same plan twice
 * - Block plan changes, upgrades, downgrades in-app
 * - Block subscription cancellation in-app
 */

import { extractProductCode } from '@/src/utils/plan-utils';
import { Subscription } from './trialService';
import { LAGO_API_URL, LAGO_API_KEY } from '@/src/constants/api';

// Types
export interface ValidationResult {
  isValid: boolean;
  message: string;
  code?: string;
  details?: any;
}

export interface SubscriptionValidationOptions {
  userId: string;
  productSlug: string;
  planCode: string;
}

/**
 * Check for active or pending subscriptions for a specific plan
 * @param userId Keycloak ID of the user
 * @param planCode The plan code to check
 * @returns Array of matching subscriptions
 */
async function checkActiveSubscriptions(userId: string, planCode: string): Promise<Subscription[]> {
  try {
    // Use the suggested endpoint with query parameters
    const url = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&plan_code=${encodeURIComponent(planCode)}&status[]=active&status[]=pending`;

    console.log(`🔍 VALIDATION - Checking active subscriptions for user ${userId} and plan ${planCode}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      console.error(`❌ VALIDATION - Error checking subscriptions: ${response.status}`);
      return [];
    }

    const data = await response.json();
    return data.subscriptions || [];
  } catch (error) {
    console.error("❌ VALIDATION - Exception checking subscriptions:", error);
    return [];
  }
}

/**
 * Check for any active subscriptions for a product
 * @param userId Keycloak ID of the user
 * @param productCode The product code to check
 * @returns Array of matching subscriptions
 */
async function checkActiveProductSubscriptions(userId: string, productCode: string): Promise<Subscription[]> {
  try {
    // Get all active subscriptions for the user
    const url = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&status[]=active&status[]=pending`;

    console.log(`🔍 VALIDATION - Checking all active subscriptions for user ${userId} for product ${productCode}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      console.error(`❌ VALIDATION - Error checking subscriptions: ${response.status}`);
      return [];
    }

    const data = await response.json();
    const subscriptions = data.subscriptions || [];

    console.log(`✅ VALIDATION - Found ${subscriptions.length} total subscriptions for user ${userId}`);

    // Filter subscriptions for the specific product
    const productSubscriptions = subscriptions.filter((sub: Subscription) => {
      const subProductCode = extractProductCode(sub.plan_code);
      return subProductCode === productCode;
    });

    console.log(`✅ VALIDATION - Found ${productSubscriptions.length} subscriptions for product ${productCode}`);

    return productSubscriptions;
  } catch (error) {
    console.error("❌ VALIDATION - Exception checking product subscriptions:", error);
    return [];
  }
}

/**
 * Validates if a user is eligible for a trial subscription
 * @param options Validation options containing userId, productSlug, and planCode
 * @returns Validation result with isValid flag and message
 */
export async function validateTrialEligibility(
  options: SubscriptionValidationOptions
): Promise<ValidationResult> {
  const { userId, productSlug, planCode } = options;

  if (!userId || !productSlug || !planCode) {
    return {
      isValid: false,
      message: "Missing required information for validation",
      code: "MISSING_PARAMS"
    };
  }

  try {
    console.log(`🔍 VALIDATION - Checking trial eligibility for user ${userId} and product ${productSlug}`);

    // Check if user already has this specific trial plan active or pending
    const trialSubscriptions = await checkActiveSubscriptions(userId, planCode);

    if (trialSubscriptions.length > 0) {
      return {
        isValid: false,
        message: "You already have an active or pending trial for this product",
        code: "HAS_ACTIVE_TRIAL",
        details: trialSubscriptions[0]
      };
    }

    // Check if user has previously used this trial plan
    // We need to check all subscriptions (including expired ones) for this plan
    const allSubscriptionsUrl = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&plan_code=${encodeURIComponent(planCode)}`;
    const allSubsResponse = await fetch(allSubscriptionsUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (allSubsResponse.ok) {
      const allSubsData = await allSubsResponse.json();
      const allSubs = allSubsData.subscriptions || [];

      if (allSubs.length > 0) {
        return {
          isValid: false,
          message: "You have already used the trial for this product",
          code: "TRIAL_ALREADY_USED",
          details: allSubs[0]
        };
      }
    }

    // Check if user has any active paid subscription for this product
    const productCode = extractProductCode(planCode);
    const productSubscriptions = await checkActiveProductSubscriptions(userId, productCode);

    // Filter out trial plans to find paid plans
    const paidSubscriptions = productSubscriptions.filter(sub => {
      return !sub.plan_code.toLowerCase().includes('trial');
    });

    if (paidSubscriptions.length > 0) {
      return {
        isValid: false,
        message: "You already have a paid subscription for this product",
        code: "HAS_PAID_PLAN",
        details: paidSubscriptions[0]
      };
    }

    // If we get here, the user is eligible for a trial
    return {
      isValid: true,
      message: "You are eligible for a trial subscription",
      code: "ELIGIBLE"
    };
  } catch (error) {
    console.error("❌ VALIDATION - Error validating trial eligibility:", error);
    return {
      isValid: false,
      message: "An error occurred while checking trial eligibility",
      code: "ERROR",
      details: error
    };
  }
}

/**
 * Validates if a user can subscribe to a paid plan
 * @param options Validation options containing userId, productSlug, and planCode
 * @returns Validation result with isValid flag and message
 */
export async function validatePaidSubscription(
  options: SubscriptionValidationOptions
): Promise<ValidationResult> {
  const { userId, productSlug, planCode } = options;

  if (!userId || !productSlug || !planCode) {
    return {
      isValid: false,
      message: "Missing required information for validation",
      code: "MISSING_PARAMS"
    };
  }

  try {
    console.log(`🔍 VALIDATION - Checking paid subscription eligibility for user ${userId} and plan ${planCode}`);

    // Check if user already has this exact plan active or pending
    const existingSubscriptions = await checkActiveSubscriptions(userId, planCode);

    if (existingSubscriptions.length > 0) {
      const subscription = existingSubscriptions[0];

      // If the subscription is active, they can't subscribe again
      if (subscription.status === 'active') {
        return {
          isValid: false,
          message: "You already have an active subscription to this plan",
          code: "DUPLICATE_SUBSCRIPTION",
          details: subscription
        };
      }

      // If the subscription is pending, they should wait
      if (subscription.status === 'pending') {
        return {
          isValid: false,
          message: "You have a pending subscription to this plan. Please wait for it to activate.",
          code: "PENDING_SUBSCRIPTION",
          details: subscription
        };
      }
    }

    // Check if user has any active subscription for this product
    const productCode = extractProductCode(planCode);
    const productSubscriptions = await checkActiveProductSubscriptions(userId, productCode);

    if (productSubscriptions.length > 0) {
      // User already has an active subscription for this product
      // We'll allow them to subscribe to a different plan of the same product
      // but log it for awareness
      console.log(`⚠️ VALIDATION - User ${userId} already has ${productSubscriptions.length} active subscriptions for product ${productCode} but is subscribing to a different plan`);

      // Return details about existing subscriptions
      return {
        isValid: true,
        message: "You can subscribe to this plan, but you already have other active subscriptions for this product.",
        code: "HAS_OTHER_SUBSCRIPTIONS",
        details: productSubscriptions
      };
    }

    // If we get here, the user can subscribe to this paid plan
    return {
      isValid: true,
      message: "You can subscribe to this plan",
      code: "ELIGIBLE"
    };
  } catch (error) {
    console.error("❌ VALIDATION - Error validating paid subscription:", error);
    return {
      isValid: false,
      message: "An error occurred while checking subscription eligibility",
      code: "ERROR",
      details: error
    };
  }
}

/**
 * Validates if a user can change their subscription plan
 * @param options Validation options containing userId, productSlug, and planCode
 * @returns Validation result with isValid flag and message
 */
export async function validatePlanChange(
  options: SubscriptionValidationOptions
): Promise<ValidationResult> {
  const { userId, productSlug, planCode } = options;

  if (!userId || !productSlug || !planCode) {
    return {
      isValid: false,
      message: "Missing required information for validation",
      code: "MISSING_PARAMS"
    };
  }

  try {
    // First check if the subscription exists
    const existingSubscriptions = await checkActiveSubscriptions(userId, planCode);

    if (existingSubscriptions.length === 0) {
      return {
        isValid: false,
        message: "No active subscription found to change.",
        code: "NO_SUBSCRIPTION"
      };
    }

    // Plan changes are not allowed in-app according to requirements
    return {
      isValid: false,
      message: "To change your subscription plan, please contact support.",
      code: "PLAN_CHANGE_NOT_ALLOWED"
    };
  } catch (error) {
    console.error("❌ VALIDATION - Error validating plan change:", error);
    return {
      isValid: false,
      message: "An error occurred while checking plan change eligibility",
      code: "ERROR",
      details: error
    };
  }
}

/**
 * Validates if a user can cancel their subscription
 * @param options Validation options containing userId, productSlug, and planCode
 * @returns Validation result with isValid flag and message
 */
export async function validateSubscriptionCancellation(
  options: SubscriptionValidationOptions
): Promise<ValidationResult> {
  const { userId, productSlug, planCode } = options;

  if (!userId || !productSlug || !planCode) {
    return {
      isValid: false,
      message: "Missing required information for validation",
      code: "MISSING_PARAMS"
    };
  }

  try {
    // First check if the subscription exists
    const existingSubscriptions = await checkActiveSubscriptions(userId, planCode);

    if (existingSubscriptions.length === 0) {
      return {
        isValid: false,
        message: "No active subscription found to cancel.",
        code: "NO_SUBSCRIPTION"
      };
    }

    // Subscription cancellation is not allowed in-app according to requirements
    return {
      isValid: false,
      message: "Subscription cancellation is currently not supported.",
      code: "CANCELLATION_NOT_ALLOWED"
    };
  } catch (error) {
    console.error("❌ VALIDATION - Error validating subscription cancellation:", error);
    return {
      isValid: false,
      message: "An error occurred while checking cancellation eligibility",
      code: "ERROR",
      details: error
    };
  }
}

/**
 * Validates if a user can switch between products
 * @param options Validation options containing userId, productSlug, and planCode
 * @returns Validation result with isValid flag and message
 */
export async function validateProductSwitch(
  options: SubscriptionValidationOptions
): Promise<ValidationResult> {
  const { userId, productSlug, planCode } = options;

  if (!userId || !productSlug || !planCode) {
    return {
      isValid: false,
      message: "Missing required information for validation",
      code: "MISSING_PARAMS"
    };
  }

  try {
    // First check if the user has an active subscription for the current product
    const productCode = extractProductCode(planCode);
    const productSubscriptions = await checkActiveProductSubscriptions(userId, productCode);

    if (productSubscriptions.length === 0) {
      return {
        isValid: false,
        message: "No active subscription found for the current product.",
        code: "NO_SUBSCRIPTION"
      };
    }

    // Product switching is not allowed according to requirements
    return {
      isValid: false,
      message: "Each product has its own isolated plans. You cannot switch between products.",
      code: "PRODUCT_SWITCH_NOT_ALLOWED"
    };
  } catch (error) {
    console.error("❌ VALIDATION - Error validating product switch:", error);
    return {
      isValid: false,
      message: "An error occurred while checking product switch eligibility",
      code: "ERROR",
      details: error
    };
  }
}

/**
 * Helper function to check if two plans belong to the same product
 * @param planCode1 First plan code
 * @param planCode2 Second plan code
 * @returns Boolean indicating if they belong to the same product
 */
export function arePlansFromSameProduct(planCode1: string, planCode2: string): boolean {
  if (!planCode1 || !planCode2) return false;

  const product1 = extractProductCode(planCode1);
  const product2 = extractProductCode(planCode2);

  return product1 === product2;
}
