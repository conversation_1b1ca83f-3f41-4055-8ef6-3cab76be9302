import { arePlansFromSameProduct, areExactSamePlan } from "@/src/utils/plan-utils";
import { signIn } from "next-auth/react";
import { CartItem } from "@/context/cart-context";

/**
 * Subscription check service to handle all subscription-related checks
 */

// Types
export interface SubscriptionInfo {
  isPaidPlan: boolean;
  isTrialPlan: boolean;
  planName?: string;
  planExpiration?: string;
  isActive: boolean;
  isExpired: boolean;
  isPending?: boolean;
  daysRemaining?: number;
}

export interface SubscriptionCheckResult {
  isSubscribed: boolean;
  subscription: SubscriptionInfo | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Check if a user is subscribed to a product
 * @param userId The user's ID
 * @param planCode The plan code to check against
 * @returns Promise with result object containing isSubscribed flag and subscription details if found
 */
export async function checkUserSubscription(
  userId: string | undefined,
  planCode: string
): Promise<SubscriptionCheckResult> {
  // Default result
  const defaultResult: SubscriptionCheckResult = {
    isSubscribed: false,
    subscription: null,
    isLoading: false,
    error: null
  };

  // If no userId or planCode, user is not subscribed
  if (!userId || !planCode) {
    console.log(`🔍 SUBSCRIPTION CHECK - Missing ${!userId ? 'userId' : 'planCode'} for subscription check`);
    return defaultResult;
  }

  console.log(`🔍 SUBSCRIPTION CHECK - Starting subscription check for user ${userId} and plan ${planCode}`);

  try {
    // Check for active/pending subscriptions for this product
    const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&status[]=active&status[]=pending`;

    console.log(`🔍 SUBSCRIPTION CHECK - Checking subscriptions for user ${userId} and plan ${planCode}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      console.error(`❌ SUBSCRIPTION CHECK - Error checking subscriptions: ${response.status}`);
      return {
        ...defaultResult,
        error: `Error checking subscriptions: ${response.status}`
      };
    }

    const data = await response.json();
    const subscriptions = data.subscriptions || [];

    console.log(`✅ SUBSCRIPTION CHECK - Found ${subscriptions.length} total subscriptions for user ${userId}`);

    // Check if any subscription is for the same product as the plan code
    const matchingSubscription = subscriptions.find(sub =>
      arePlansFromSameProduct(sub.plan_code, planCode)
    );

    if (matchingSubscription) {
      console.log(`⚠️ SUBSCRIPTION CHECK - User already has a subscription for the same product as ${planCode}: ${matchingSubscription.plan_code}`);

      return {
        isSubscribed: true,
        subscription: {
          isPaidPlan: !matchingSubscription.plan_code.includes('trial'),
          isTrialPlan: matchingSubscription.plan_code.includes('trial'),
          planName: matchingSubscription.name || matchingSubscription.plan_code,
          planExpiration: matchingSubscription.ending_at,
          isActive: matchingSubscription.status === 'active',
          isExpired: false,
          isPending: matchingSubscription.status === 'pending',
          daysRemaining: matchingSubscription.ending_at ?
            Math.ceil((new Date(matchingSubscription.ending_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) :
            undefined
        },
        isLoading: false,
        error: null
      };
    }

    return defaultResult;
  } catch (error) {
    console.error("❌ SUBSCRIPTION CHECK - Error checking subscription:", error);
    return {
      ...defaultResult,
      error: error instanceof Error ? error.message : "Unknown error checking subscription"
    };
  }
}

/**
 * Check if a cart already contains a plan for the same product
 * @param cartItems Array of cart items
 * @param planCode The plan code to check against
 * @returns Boolean indicating if the cart contains a plan for the same product
 */
export function isProductInCart(cartItems: CartItem[], planCode: string): boolean {
  if (!cartItems || !planCode) return false;

  return cartItems.some(item =>
    item.planCode && arePlansFromSameProduct(item.planCode, planCode)
  );
}

/**
 * Check if a cart already contains the exact same plan
 * @param cartItems Array of cart items
 * @param planCode The plan code to check against
 * @returns Boolean indicating if the cart contains the exact same plan
 */
export function isExactPlanInCart(cartItems: CartItem[], planCode: string): boolean {
  if (!cartItems || !planCode) return false;

  return cartItems.some(item =>
    item.planCode && areExactSamePlan(item.planCode, planCode)
  );
}

/**
 * Find a different plan from the same product in the cart
 * @param cartItems Array of cart items
 * @param planCode The plan code to check against
 * @returns The cart item if found, undefined otherwise
 */
export function findDifferentPlanSameProduct(cartItems: CartItem[], planCode: string): CartItem | undefined {
  if (!cartItems || !planCode) return undefined;

  return cartItems.find(item =>
    item.planCode &&
    arePlansFromSameProduct(item.planCode, planCode) &&
    !areExactSamePlan(item.planCode, planCode)
  );
}

/**
 * Handle subscription flow based on user authentication and subscription status
 * @param params Parameters for handling subscription
 * @returns Object with result of the operation
 */
export async function handleSubscription({
  userId,
  planCode,
  planName,
  planPrice,
  planDuration,
  productSlug,
  cartItems,
  addToCart,
  onShowSubscriptionInfo,
  onShowLoginPrompt
}: {
  userId?: string;
  planCode: string;
  planName: string;
  planPrice: number;
  planDuration: "monthly" | "yearly" | "trial";
  productSlug: string;
  cartItems: CartItem[];
  addToCart: (item: CartItem) => void;
  onShowSubscriptionInfo?: (subscription: SubscriptionInfo) => void;
  onShowLoginPrompt?: () => void;
}) {
  // Step 1: Check if user is logged in
  console.log(`🔍 SUBSCRIPTION CHECK - User authentication check: userId=${userId ? 'present' : 'missing'}`);

  if (!userId) {
    console.log("🔍 SUBSCRIPTION CHECK - User not logged in, storing plan info for post-login check");

    // Store the plan info in localStorage for post-login processing
    try {
      localStorage.setItem("pending_subscription_plan", planCode);
      localStorage.setItem("pending_subscription_product", planCode.split('_')[0]);
      localStorage.setItem("pending_subscription_slug", productSlug);
      localStorage.setItem("pending_subscription_name", planName);
      localStorage.setItem("pending_subscription_price", planPrice.toString());
      localStorage.setItem("pending_subscription_duration", planDuration);

      // If callback provided, use it, otherwise redirect to login
      if (onShowLoginPrompt) {
        onShowLoginPrompt();
      } else {
        // Redirect to sign in page with return URL to come back to this product
        const returnUrl = `/products/${productSlug}`;
        signIn("keycloak", { callbackUrl: returnUrl });
      }

      return { success: true, action: "login_redirect" };
    } catch (error) {
      console.error("❌ SUBSCRIPTION CHECK - Error storing pending subscription info:", error);
      return {
        success: false,
        action: "error",
        error: "Failed to prepare subscription. Please try again."
      };
    }
  }

  // Step 2: Check if user already has a subscription for this product
  console.log(`🔍 SUBSCRIPTION CHECK - Checking if user ${userId} has subscription for plan ${planCode}`);
  const subscriptionResult = await checkUserSubscription(userId, planCode);

  console.log(`🔍 SUBSCRIPTION CHECK - Subscription check result: isSubscribed=${subscriptionResult.isSubscribed}`);

  if (subscriptionResult.isSubscribed && subscriptionResult.subscription) {
    console.log(`🔍 SUBSCRIPTION CHECK - User already subscribed to ${planCode}`);

    // If callback provided, use it
    if (onShowSubscriptionInfo) {
      onShowSubscriptionInfo(subscriptionResult.subscription);
    }

    return {
      success: true,
      action: "show_subscription_info",
      subscription: subscriptionResult.subscription
    };
  }

  // Step 3: Check if the exact same plan is already in the cart
  if (isExactPlanInCart(cartItems, planCode)) {
    console.log(`🔍 SUBSCRIPTION CHECK - Exact same plan already in cart: ${planCode}`);
    return { success: true, action: "already_in_cart", message: "This plan is already in your cart" };
  }

  // Step 4: Check if a different plan from the same product is in the cart
  const differentPlan = findDifferentPlanSameProduct(cartItems, planCode);
  if (differentPlan) {
    console.log(`🔍 SUBSCRIPTION CHECK - Different plan from same product in cart: ${differentPlan.planCode}`);
    return {
      success: true,
      action: "different_plan_in_cart",
      existingPlan: differentPlan,
      message: "You have a different plan for this product in your cart"
    };
  }

  // Step 4: Add to cart
  const newItem: CartItem = {
    id: planName,
    name: planName,
    planCode,
    price: planPrice,
    quantity: 1,
    planDuration
  };

  addToCart(newItem);
  return { success: true, action: "added_to_cart" };
}

/**
 * Process pending subscription after login
 * @param params Parameters for processing pending subscription
 * @returns Object with result of the operation
 */
export async function processPendingSubscription({
  userId,
  cartItems,
  addToCart,
  onShowSubscriptionInfo
}: {
  userId: string;
  cartItems: CartItem[];
  addToCart: (item: CartItem) => void;
  onShowSubscriptionInfo?: (subscription: SubscriptionInfo) => void;
}) {
  // Check if we have pending subscription info
  const pendingPlan = localStorage.getItem("pending_subscription_plan");
  const pendingProduct = localStorage.getItem("pending_subscription_product");
  const pendingSlug = localStorage.getItem("pending_subscription_slug");
  const pendingName = localStorage.getItem("pending_subscription_name") || "";
  const pendingPrice = parseFloat(localStorage.getItem("pending_subscription_price") || "0");
  const pendingDuration = localStorage.getItem("pending_subscription_duration") as "monthly" | "yearly" | "trial" || "monthly";

  // Only proceed if we have the necessary data
  if (!pendingPlan || !pendingProduct || !pendingSlug) {
    return { success: false, action: "no_pending_subscription" };
  }

  console.log(`🔍 SUBSCRIPTION CHECK - Processing pending subscription: ${pendingPlan}`);

  // Check if user already has a subscription for this product
  const subscriptionResult = await checkUserSubscription(userId, pendingPlan);

  if (subscriptionResult.isSubscribed && subscriptionResult.subscription) {
    console.log(`🔍 SUBSCRIPTION CHECK - User already subscribed to ${pendingPlan}`);

    // Clear pending subscription info
    clearPendingSubscriptionInfo();

    // If callback provided, use it
    if (onShowSubscriptionInfo) {
      onShowSubscriptionInfo(subscriptionResult.subscription);
    }

    return {
      success: true,
      action: "show_subscription_info",
      subscription: subscriptionResult.subscription
    };
  }

  // Check if the exact same plan is already in the cart
  if (isExactPlanInCart(cartItems, pendingPlan)) {
    console.log(`🔍 SUBSCRIPTION CHECK - Exact same plan already in cart: ${pendingPlan}`);
    clearPendingSubscriptionInfo();
    return { success: true, action: "already_in_cart", message: "This plan is already in your cart" };
  }

  // Check if a different plan from the same product is in the cart
  const differentPlan = findDifferentPlanSameProduct(cartItems, pendingPlan);
  if (differentPlan) {
    console.log(`🔍 SUBSCRIPTION CHECK - Different plan from same product in cart: ${differentPlan.planCode}`);
    // Don't clear pending subscription info yet, as we'll need it for the plan change dialog
    return {
      success: true,
      action: "different_plan_in_cart",
      existingPlan: differentPlan,
      pendingPlan: {
        planCode: pendingPlan,
        name: pendingName,
        price: pendingPrice,
        duration: pendingDuration,
        slug: pendingSlug
      },
      message: "You have a different plan for this product in your cart"
    };
  }

  // Add to cart
  const newItem: CartItem = {
    id: pendingName,
    name: pendingName,
    planCode: pendingPlan,
    price: pendingPrice,
    quantity: 1,
    planDuration: pendingDuration,
    slug: pendingSlug
  };

  addToCart(newItem);
  clearPendingSubscriptionInfo();
  return { success: true, action: "added_to_cart" };
}

/**
 * Clear pending subscription info from localStorage
 */
export function clearPendingSubscriptionInfo() {
  localStorage.removeItem("pending_subscription_plan");
  localStorage.removeItem("pending_subscription_product");
  localStorage.removeItem("pending_subscription_slug");
  localStorage.removeItem("pending_subscription_name");
  localStorage.removeItem("pending_subscription_price");
  localStorage.removeItem("pending_subscription_duration");
}
