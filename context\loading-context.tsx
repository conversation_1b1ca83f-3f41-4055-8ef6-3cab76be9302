"use client";

import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';

interface LoadingContextType {
  isLoading: boolean;
  setLoading: (isLoading: boolean) => void;
  startLoading: () => void;
  stopLoading: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const initialLoadRef = useRef(true);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startLoading = () => setIsLoading(true);
  const stopLoading = () => setIsLoading(false);

  // Handle initial page load
  useEffect(() => {
    if (initialLoadRef.current) {
      // Set a minimum loading time for initial page load
      const timer = setTimeout(() => {
        stopLoading();
        initialLoadRef.current = false;
      }, 1500); // Longer initial loading time
      
      return () => clearTimeout(timer);
    }
  }, []);

  // Monitor route changes to show loading indicator
  useEffect(() => {
    // Clean up any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Don't show loader for initial render (handled above)
    if (!initialLoadRef.current) {
      startLoading();
      
      // Set a timeout to hide loader
      timeoutRef.current = setTimeout(() => {
        stopLoading();
      }, 800);
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [pathname, searchParams]);

  // Listen for browser back/forward navigation
  useEffect(() => {
    const handleStart = () => {
      startLoading();
    };
    
    const handleComplete = () => {
      // Add a small delay to ensure content has rendered
      setTimeout(stopLoading, 300);
    };

    window.addEventListener('popstate', handleStart);
    
    return () => {
      window.removeEventListener('popstate', handleStart);
    };
  }, [router]);

  const value = {
    isLoading,
    setLoading: setIsLoading,
    startLoading,
    stopLoading,
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
} 