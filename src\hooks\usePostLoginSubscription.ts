import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useCart } from '@/context/cart-context';
import { checkUserSubscription, SubscriptionInfo, clearPendingSubscriptionInfo } from '@/src/services/subscriptionCheckService';
import { arePlansFromSameProduct } from '@/src/utils/plan-utils';
import { toast } from 'sonner';

/**
 * Custom hook to handle post-login subscription processing
 * @returns Object with subscription info and dialog state
 */
export function usePostLoginSubscription() {
  const { data: session, status } = useSession();
  const { items, addItem } = useCart();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSubscriptionInfoOpen, setIsSubscriptionInfoOpen] = useState(false);
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);

  // Process pending subscription when user logs in
  useEffect(() => {
    const processPending = async () => {
      // Only proceed if user is authenticated and we're not already processing
      if (status !== 'authenticated' || !session?.user?.id || isProcessing) return;

      // Check if we have pending subscription info
      const pendingPlan = localStorage.getItem("pending_subscription_plan");
      if (!pendingPlan) return;

      setIsProcessing(true);

      try {
        console.log(`🔍 POST-LOGIN - Processing pending subscription: ${pendingPlan}`);

        // First, check if user already has a subscription for this product
        const checkResult = await checkUserSubscription(session.user.id, pendingPlan);

        if (checkResult.isSubscribed && checkResult.subscription) {
          // User already has a subscription - show dialog
          console.log(`🔍 POST-LOGIN - User already has subscription for ${pendingPlan}`);
          setSubscription(checkResult.subscription);
          setIsSubscriptionInfoOpen(true);
          toast.info("You already have an active subscription for this product.");

          // Clear pending subscription info
          clearPendingSubscriptionInfo();
        } else {
          // User doesn't have a subscription - check if product is in cart
          const pendingProduct = localStorage.getItem("pending_subscription_product");
          const pendingSlug = localStorage.getItem("pending_subscription_slug");
          const pendingName = localStorage.getItem("pending_subscription_name") || "";
          const pendingPrice = parseFloat(localStorage.getItem("pending_subscription_price") || "0");
          const pendingDuration = localStorage.getItem("pending_subscription_duration") as "monthly" | "yearly" | "trial" || "monthly";

          // Check if product is already in cart
          const productInCart = items.some(item =>
            item.planCode && arePlansFromSameProduct(item.planCode, pendingPlan)
          );

          if (productInCart) {
            console.log(`🔍 POST-LOGIN - Product already in cart: ${pendingPlan}`);
            toast.info("You already have a plan for this product in your cart.", {
              duration: 5000,
              action: {
                label: "View Cart",
                onClick: () => window.location.href = "/cart"
              }
            });
          } else {
            // Add to cart
            console.log(`🔍 POST-LOGIN - Adding plan to cart: ${pendingPlan}`);
            const newItem = {
              id: pendingName,
              name: pendingName,
              planCode: pendingPlan,
              price: pendingPrice,
              quantity: 1,
              planDuration: pendingDuration,
              slug: pendingSlug
            };

            addItem(newItem);
            toast.success(`${pendingName} plan added to cart`);
          }

          // Clear pending subscription info
          clearPendingSubscriptionInfo();
        }
      } catch (error) {
        console.error("Error processing pending subscription:", error);
        toast.error("Something went wrong. Please try again.");
      } finally {
        setIsProcessing(false);
      }
    };

    processPending();
  }, [status, session, items, addItem, isProcessing]);

  return {
    isSubscriptionInfoOpen,
    setIsSubscriptionInfoOpen,
    subscription,
    isProcessing
  };
}
