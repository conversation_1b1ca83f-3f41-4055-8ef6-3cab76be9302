/**
 * Email Service
 *
 * Handles all email-related functionality, including sending invoice payment
 * confirmation emails through the external email API.
 */

// Constants for API endpoints
const EMAIL_API_URL = 'https://onepay.cubeone.in/api/send-subscription-email';
const EMAIL_API_TOKEN = '1231934a-c657-48d3-882e-125e618eaf1b';

// Store sent email IDs to prevent duplicates
// In-memory cache for the current server instance
const sentEmails = new Set<string>();

/**
 * Check if an email has already been sent for an invoice
 * Uses both in-memory cache and localStorage (for client-side) to prevent duplicates
 *
 * @param invoiceId The invoice ID to check
 * @returns Boolean indicating if the email has already been sent
 */
function hasEmailBeenSent(invoiceId: string): boolean {
  const emailKey = `email_${invoiceId}`;

  // Check in-memory cache first
  if (sentEmails.has(emailKey)) {
    return true;
  }

  // For client-side, also check localStorage
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const sentEmailsJson = localStorage.getItem('sent_invoice_emails');
      if (sentEmailsJson) {
        const sentEmailsArray = JSON.parse(sentEmailsJson);
        if (Array.isArray(sentEmailsArray) && sentEmailsArray.includes(invoiceId)) {
          // Add to in-memory cache as well
          sentEmails.add(emailKey);
          return true;
        }
      }
    } catch (error) {
      console.error('Error checking localStorage for sent emails:', error);
    }
  }

  return false;
}

/**
 * Mark an email as sent to prevent duplicates
 * Updates both in-memory cache and localStorage (for client-side)
 *
 * @param invoiceId The invoice ID to mark as sent
 */
function markEmailAsSent(invoiceId: string): void {
  const emailKey = `email_${invoiceId}`;

  // Add to in-memory cache
  sentEmails.add(emailKey);

  // For client-side, also update localStorage
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const sentEmailsJson = localStorage.getItem('sent_invoice_emails');
      let sentEmailsArray = sentEmailsJson ? JSON.parse(sentEmailsJson) : [];

      if (!Array.isArray(sentEmailsArray)) {
        sentEmailsArray = [];
      }

      if (!sentEmailsArray.includes(invoiceId)) {
        sentEmailsArray.push(invoiceId);
        localStorage.setItem('sent_invoice_emails', JSON.stringify(sentEmailsArray));
      }
    } catch (error) {
      console.error('Error updating localStorage for sent emails:', error);
    }
  }
}

/**
 * Interface for email sending response
 */
export interface EmailResponse {
  success: boolean;
  message: string;
  error?: string;
  invoice_id?: string;
}

/**
 * Send an invoice payment confirmation email
 *
 * @param invoiceId The Lago invoice ID
 * @param trackingId Optional tracking ID for logging
 * @returns Promise with the email sending result
 */
export async function sendInvoicePaymentEmail(
  invoiceId: string,
  trackingId?: string
): Promise<EmailResponse> {
  if (!invoiceId) {
    console.error('❌ EMAIL SERVICE - Cannot send invoice email: Missing invoice ID');
    return {
      success: false,
      message: 'Missing invoice ID',
      error: 'MISSING_INVOICE_ID'
    };
  }

  const logPrefix = trackingId ? `📧 EMAIL [${trackingId}]` : '📧 EMAIL';
  console.log(`${logPrefix} - Sending invoice payment email for invoice: ${invoiceId}`);

  try {
    // Call the server-side API route to avoid CORS issues
    const response = await fetch('/api/email/invoice', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        invoice_id: invoiceId
      })
    });

    console.log(`${logPrefix} - Email API response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`${logPrefix} - Successfully sent invoice payment email: ${invoiceId}`);
      return {
        success: true,
        message: data.message || 'Email sent successfully',
        invoice_id: invoiceId
      };
    } else {
      const errorText = await response.text();
      console.error(`${logPrefix} - API error sending email: ${response.status}`, errorText);
      return {
        success: false,
        message: 'Failed to send email',
        error: errorText,
        invoice_id: invoiceId
      };
    }
  } catch (error) {
    console.error(`${logPrefix} - Exception sending email:`, error);
    return {
      success: false,
      message: 'Exception sending email',
      error: error instanceof Error ? error.message : String(error),
      invoice_id: invoiceId
    };
  }
}

/**
 * Send an invoice payment confirmation email (server-side)
 *
 * This version is for server-side use and directly calls the external API
 *
 * @param invoiceId The Lago invoice ID
 * @param trackingId Optional tracking ID for logging
 * @returns Promise with the email sending result
 */
export async function sendInvoicePaymentEmailServer(
  invoiceId: string,
  trackingId?: string
): Promise<EmailResponse> {
  if (!invoiceId) {
    console.error('❌ EMAIL SERVICE - Cannot send invoice email: Missing invoice ID');
    return {
      success: false,
      message: 'Missing invoice ID',
      error: 'MISSING_INVOICE_ID'
    };
  }

  // Check if this email has already been sent to prevent duplicates
  if (hasEmailBeenSent(invoiceId)) {
    console.log(`📧 EMAIL - Email for invoice ${invoiceId} already sent, skipping duplicate`);
    return {
      success: true,
      message: 'Email already sent (deduplicated)',
      invoice_id: invoiceId
    };
  }

  const logPrefix = trackingId ? `📧 EMAIL [${trackingId}]` : '📧 EMAIL';
  const requestId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

  console.log(`${logPrefix} [${requestId}] - Sending invoice payment email (server) for invoice: ${invoiceId}`);
  console.log(`${logPrefix} [${requestId}] - Using external email API URL: ${EMAIL_API_URL}`);

  try {
    // Prepare the request payload
    const payload = {
      lago_invoice_id: invoiceId
    };

    console.log(`${logPrefix} [${requestId}] - Request payload:`, payload);
    console.log(`${logPrefix} [${requestId}] - Using Authorization Bearer token`);

    // Build the URL with query parameters
    const url = new URL(EMAIL_API_URL);
    url.searchParams.append('lago_invoice_id', invoiceId);

    console.log(`${logPrefix} [${requestId}] - Full URL with params: ${url.toString()}`);

    // Call the external email API with GET request and Authorization header
    // Note: We're using GET with query parameters instead of body
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${EMAIL_API_TOKEN}`,
        'X-Request-ID': requestId
      }
      // No body for GET request - using query parameters instead
    });

    console.log(`${logPrefix} [${requestId}] - External email API response status: ${response.status}`);

    // Get the response body as text first for logging
    const responseText = await response.text();
    console.log(`${logPrefix} [${requestId}] - Raw response body:`, responseText);

    // Try to parse as JSON if possible
    let responseData;
    try {
      responseData = JSON.parse(responseText);
      console.log(`${logPrefix} [${requestId}] - Parsed response data:`, responseData);
    } catch (parseError) {
      console.log(`${logPrefix} [${requestId}] - Response is not valid JSON`);
    }

    if (response.ok) {
      // Mark this email as sent to prevent duplicates
      markEmailAsSent(invoiceId);

      console.log(`${logPrefix} [${requestId}] - Successfully sent invoice payment email: ${invoiceId}`);
      return {
        success: true,
        message: responseData?.message || 'Email sent successfully',
        invoice_id: invoiceId
      };
    } else {
      console.error(`${logPrefix} [${requestId}] - External API error sending email: ${response.status}`, responseText);
      return {
        success: false,
        message: 'Failed to send email',
        error: responseText,
        invoice_id: invoiceId
      };
    }
  } catch (error) {
    console.error(`${logPrefix} [${requestId}] - Exception sending email:`, error);
    return {
      success: false,
      message: 'Exception sending email',
      error: error instanceof Error ? error.message : String(error),
      invoice_id: invoiceId
    };
  }
}

/**
 * Retry sending an invoice payment email with exponential backoff
 *
 * @param invoiceId The Lago invoice ID
 * @param maxRetries Maximum number of retry attempts
 * @param trackingId Optional tracking ID for logging
 * @returns Promise with the email sending result
 */
export async function sendInvoicePaymentEmailWithRetry(
  invoiceId: string,
  maxRetries: number = 3,
  trackingId?: string
): Promise<EmailResponse> {
  const logPrefix = trackingId ? `📧 EMAIL [${trackingId}]` : '📧 EMAIL';

  let lastError: EmailResponse = {
    success: false,
    message: 'All retry attempts failed',
    error: 'MAX_RETRIES_EXCEEDED',
    invoice_id: invoiceId
  };

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`${logPrefix} - Retry attempt ${attempt + 1}/${maxRetries} for invoice: ${invoiceId}`);

      // Wait with exponential backoff before retrying (except first attempt)
      if (attempt > 0) {
        const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
        console.log(`${logPrefix} - Waiting ${delayMs}ms before retry attempt ${attempt + 1}`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }

      // Attempt to send the email
      const result = await sendInvoicePaymentEmailServer(invoiceId, trackingId);

      if (result.success) {
        console.log(`${logPrefix} - Email sent successfully on attempt ${attempt + 1}/${maxRetries}`);
        return result;
      }

      lastError = result;
      console.error(`${logPrefix} - Attempt ${attempt + 1}/${maxRetries} failed:`, result.error);
    } catch (error) {
      console.error(`${logPrefix} - Exception during retry attempt ${attempt + 1}/${maxRetries}:`, error);
      lastError = {
        success: false,
        message: 'Exception during retry',
        error: error instanceof Error ? error.message : String(error),
        invoice_id: invoiceId
      };
    }
  }

  console.error(`${logPrefix} - All ${maxRetries} retry attempts failed for invoice: ${invoiceId}`);
  return lastError;
}
