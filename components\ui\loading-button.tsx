"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ButtonProps } from '@radix-ui/react-slot';

interface LoadingButtonProps extends Omit<ButtonProps, 'asChild'> {
  isLoading?: boolean;
  loadingText?: string;
  className?: string;
  spinnerClassName?: string;
  spinnerSize?: "sm" | "md" | "lg";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  children: React.ReactNode;
}

export function LoadingButton({
  isLoading = false,
  loadingText = "Loading...",
  className,
  spinnerClassName,
  spinnerSize = "sm",
  variant = "default",
  children,
  disabled,
  ...props
}: LoadingButtonProps) {
  // Size mappings for spinner
  const spinnerSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  return (
    <Button
      disabled={isLoading || disabled}
      className={cn(
        "relative",
        className
      )}
      variant={variant}
      {...props}
    >
      {isLoading ? (
        <>
          <span 
            className={cn(
              "mr-2 animate-spin rounded-full border-2 border-current border-t-transparent",
              spinnerSizes[spinnerSize],
              spinnerClassName
            )}
          />
          {loadingText || children}
        </>
      ) : children}
    </Button>
  );
}

export default LoadingButton; 