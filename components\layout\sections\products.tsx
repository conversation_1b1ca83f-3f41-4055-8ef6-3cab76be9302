"use client";
import React, { useEffect, useState } from "react";
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON><PERSON> } from "@/components/ui/button";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";
import { PinContainer } from "../../ui/3d-pin";
import { ArrowRight, Loader2 } from "lucide-react";
import type { ProductResponse } from "../../../@types/product";

// Import Apollo client but with error handling
// Define a type for the Apollo client to fix TypeScript errors
interface ApolloClientType {
  query: (options: { query: any; fetchPolicy: string }) => Promise<{ data: any }>;
}

let client: ApolloClientType | undefined;
try {
  // Use dynamic import to avoid ESM/CJS issues
  client = require("@/lib/apolloClient").default;
} catch (e) {
  console.error("Failed to import Apollo client:", e);
}

// Define the GraphQL query as a string
const GET_PRODUCTS_QUERY = `
  query GetProducts {
    products {
      slug
      ProductCard {
        productName
        productDescription
        productLogo {
          url
        }
      }
    }
  }
`;

export function ProductsSection() {
  const [products, setProducts] = useState<ProductResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        // First try to use Apollo Client if available
        if (client) {
          try {
            const { data } = await client.query({
              query: require('@apollo/client').gql`${GET_PRODUCTS_QUERY}`,
              fetchPolicy: "no-cache",
            });
            setProducts(data.products);
            return; // Exit early if successful
          } catch (apolloError) {
            console.error('Apollo client error:', apolloError);
            // Fall through to fetch API approach
          }
        }

        // Fallback to standard fetch API if Apollo fails or isn't available
        const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_API_URL;
        if (!apiUrl) {
          throw new Error('GraphQL API URL not configured');
        }
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: GET_PRODUCTS_QUERY,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }

        setProducts(result.data.products);
      } catch (error) {
        console.error('Failed to load products:', error);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  if (loading) {
    return (
      <SectionContainer id="products">
        <SectionHeader
          subTitle="Products"
          title="Our Exclusive Products"
          description="Explore our range of innovative products designed to meet your needs and exceed your expectations."
        />
        <div className="flex justify-center items-center min-h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </SectionContainer>
    );
  }

  if (error) {
    return (
      <SectionContainer id="products">
        <SectionHeader
          subTitle="Products"
          title="Our Exclusive Products"
          description="Explore our range of innovative products designed to meet your needs and exceed your expectations."
        />
        <div className="text-center text-red-500 my-8">{error}</div>
      </SectionContainer>
    );
  }

  const displayedProducts = products.slice(0, 3);

  return (
    <SectionContainer id="products">
      <SectionHeader
        subTitle="Products"
        title="Our Exclusive Products"
        description="Explore our range of innovative products designed to meet your needs and exceed your expectations."
      />
      <div className="relative z-0 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8 md:gap-12 lg:gap-16 my-4 sm:my-8 px-2 sm:px-6 lg:px-8">
        {displayedProducts.map((product) => (
          <div key={product.slug} className="min-h-[12rem] sm:min-h-[15rem] sm:h-[18rem] lg:h-[20rem] w-full flex items-center justify-center">
            <PinContainer title={product.slug} href={`/products/${product.slug}`}>
              <div className="flex basis-full flex-col p-3 sm:p-4 tracking-tight text-muted-foreground w-[22rem] sm:w-[18rem] lg:w-[20rem] h-full">
                <h3 className="max-w-xs !pb-2 !m-0 font-bold text-sm sm:text-base text-foreground">
                  {product.ProductCard.productName}
                </h3>
                <div className="text-sm sm:text-base !m-0 !p-0 font-normal mb-4">
                  <span className="text-muted-foreground">{product.ProductCard.productDescription || 'No description available'}</span>
                </div>
                <div className="grow hidden md:block relative">
                  <div className="h-full min-h-[160px] w-full rounded-lg bg-gradient-to-br from-red-50 via-pink-50 to-rose-100 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800" />
                  {product.ProductCard.productLogo && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Image 
                        src={product.ProductCard.productLogo.url} 
                        alt={product.ProductCard.productName}
                        width={200}
                        height={200}
                        className="h-3/4 w-3/4 object-contain"
                      />
                    </div>
                  )}
                </div>
              </div>
            </PinContainer>
          </div>
        ))}
      </div>
      
      {products.length > 3 && (
        <div className="flex justify-center mt-8">
          <Link href="/products">
            <Button variant="outline" size="lg" className="group">
              View All Products
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </div>
      )}
    </SectionContainer>
  );
}
