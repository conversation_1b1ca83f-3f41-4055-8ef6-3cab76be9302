'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowRight, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { InteractiveGridPattern } from '@/components/magicui/interactive-grid-pattern';
import { AuroraText } from '@/components/magicui/aurora-text';

const SignupPage = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    const fullPhoneNumber = `+91${phoneNumber}`;

    // ...signup logic...

    setIsLoading(false);
    router.push('/login');
  };

  return (
    <section className="relative w-full bg-background/95 dark:bg-background min-h-screen isolate overflow-hidden">
      <div className="absolute inset-0 -z-10">
        <InteractiveGridPattern
          className={cn(
            "[mask-image:radial-gradient(600px_circle_at_center,white,transparent)]",
            "absolute inset-x-0 inset-y-[-30%] h-[160%] w-full opacity-20 dark:opacity-30 skew-y-12"
          )}
          squares={[20, 20]}
          width={60}
          height={60}
        />
      </div>

      <div className="container relative flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold tracking-tight mb-4">
              <AuroraText className="text-primary">Create Account</AuroraText>
            </h1>
            <p className="text-lg text-muted-foreground">
              Enter your details to create a new account
            </p>
          </div>

          <div className="bg-card/50 backdrop-blur-sm border rounded-2xl p-8 shadow-lg">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-card-foreground mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">+91</span>
                  <input
                    type="text"
                    id="phoneNumber"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="w-full px-4 py-3 pl-14 rounded-lg bg-background/50 border border-input ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-card-foreground mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 pl-10 rounded-lg bg-background/50 border border-input ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all"
                    placeholder="Enter password"
                  />
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 size-4 text-muted-foreground" />
                </div>
              </div>
              
              {error && (
                <p className="text-destructive text-sm">{error}</p>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className={cn(
                  "w-full inline-flex items-center justify-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium",
                  "transition-colors hover:bg-primary/90 disabled:opacity-50 group"
                )}
              >
                {isLoading ? 'Loading...' : 'Sign Up'}
                <ArrowRight className="ml-2 size-4 transition-transform group-hover:translate-x-0.5" />
              </button>
            </form>
            <p className="text-center mt-4">
              Already have an account? <a href="/login" className="text-primary">Login</a>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SignupPage;
